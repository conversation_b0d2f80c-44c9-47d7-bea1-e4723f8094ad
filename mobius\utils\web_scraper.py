"""
Advanced Web Scraping Utilities for Mobius AI Assistant
Provides intelligent content extraction and web scraping capabilities
"""
import asyncio
import aiohttp
import logging
import time
import re
from typing import Dict, Any, List, Optional, Union
from bs4 import BeautifulSoup, Tag
from urllib.parse import urljoin, urlparse
from ..config.settings import WEB_CONFIG

logger = logging.getLogger(__name__)

class WebScraper:
    """Advanced web scraper with intelligent content extraction"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.last_request_time = 0
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self._get_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if self.session is None or self.session.closed:
            headers = {
                "User-Agent": WEB_CONFIG["user_agent"],
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1"
            }
            timeout = aiohttp.ClientTimeout(total=WEB_CONFIG["timeout"])
            self.session = aiohttp.ClientSession(
                headers=headers,
                timeout=timeout
            )
        return self.session
    
    async def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < WEB_CONFIG["rate_limit"]:
            await asyncio.sleep(WEB_CONFIG["rate_limit"] - time_since_last)
        
        self.last_request_time = time.time()
    
    async def fetch_page(self, url: str, retries: int = None) -> Optional[str]:
        """Fetch a web page with retries"""
        if retries is None:
            retries = WEB_CONFIG["max_retries"]
        
        for attempt in range(retries + 1):
            try:
                await self._rate_limit()
                session = await self._get_session()
                
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.text()
                    elif response.status == 429:  # Rate limited
                        if attempt < retries:
                            wait_time = 2 ** attempt  # Exponential backoff
                            logger.warning(f"Rate limited, waiting {wait_time}s before retry")
                            await asyncio.sleep(wait_time)
                            continue
                    else:
                        logger.warning(f"HTTP {response.status} for {url}")
                        
            except Exception as e:
                logger.error(f"Attempt {attempt + 1} failed for {url}: {str(e)}")
                if attempt < retries:
                    await asyncio.sleep(1)
                    continue
                else:
                    raise
        
        return None
    
    async def extract_content(self, url: str, content_type: str = "auto") -> Dict[str, Any]:
        """Extract content from URL with intelligent parsing"""
        html = await self.fetch_page(url)
        
        if not html:
            return {"error": "Failed to fetch page"}
        
        soup = BeautifulSoup(html, 'html.parser')
        
        # Remove unwanted elements
        self._clean_soup(soup)
        
        result = {
            "url": url,
            "title": self._extract_title(soup),
            "meta_description": self._extract_meta_description(soup),
            "content": "",
            "links": [],
            "images": [],
            "structured_data": {}
        }
        
        if content_type == "auto":
            content_type = self._detect_content_type(soup)
        
        if content_type == "article":
            result["content"] = self._extract_article_content(soup)
        elif content_type == "product":
            result.update(self._extract_product_info(soup))
        elif content_type == "social":
            result.update(self._extract_social_content(soup))
        else:
            result["content"] = self._extract_general_content(soup)
        
        # Extract links and images
        result["links"] = self._extract_links(soup, url)
        result["images"] = self._extract_images(soup, url)
        
        return result
    
    def _clean_soup(self, soup: BeautifulSoup):
        """Remove unwanted elements from soup"""
        # Remove script and style elements
        for element in soup(["script", "style", "nav", "header", "footer", "aside"]):
            element.decompose()
        
        # Remove elements with common ad/tracking classes
        ad_classes = ["ad", "advertisement", "banner", "popup", "modal", "cookie", "tracking"]
        for class_name in ad_classes:
            for element in soup.find_all(attrs={"class": re.compile(class_name, re.I)}):
                element.decompose()
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extract page title"""
        # Try different title sources
        title_sources = [
            soup.find("meta", property="og:title"),
            soup.find("meta", name="twitter:title"),
            soup.find("h1"),
            soup.find("title")
        ]
        
        for source in title_sources:
            if source:
                if source.name == "meta":
                    title = source.get("content", "")
                else:
                    title = source.get_text(strip=True)
                
                if title:
                    return title
        
        return "No title found"
    
    def _extract_meta_description(self, soup: BeautifulSoup) -> str:
        """Extract meta description"""
        meta_desc = soup.find("meta", attrs={"name": "description"})
        if meta_desc:
            return meta_desc.get("content", "")
        
        og_desc = soup.find("meta", property="og:description")
        if og_desc:
            return og_desc.get("content", "")
        
        return ""
    
    def _detect_content_type(self, soup: BeautifulSoup) -> str:
        """Detect the type of content on the page"""
        # Check for article indicators
        if soup.find("article") or soup.find(class_=re.compile("article|post|content", re.I)):
            return "article"
        
        # Check for product indicators
        if (soup.find(class_=re.compile("price|product|buy|cart", re.I)) or
            soup.find("meta", property="product:price")):
            return "product"
        
        # Check for social media indicators
        if soup.find(class_=re.compile("tweet|post|status|feed", re.I)):
            return "social"
        
        return "general"
    
    def _extract_article_content(self, soup: BeautifulSoup) -> str:
        """Extract main article content"""
        # Try to find article content
        content_selectors = [
            "article",
            "[role='main']",
            ".content",
            ".article-content",
            ".post-content",
            ".entry-content",
            "main"
        ]
        
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                return self._clean_text(content_elem.get_text())
        
        # Fallback to body content
        body = soup.find("body")
        if body:
            return self._clean_text(body.get_text())
        
        return "No content found"
    
    def _extract_general_content(self, soup: BeautifulSoup) -> str:
        """Extract general page content"""
        # Remove navigation and sidebar elements
        for elem in soup.find_all(["nav", "aside", "header", "footer"]):
            elem.decompose()
        
        # Get main content
        main = soup.find("main") or soup.find("body")
        if main:
            text = main.get_text()
            return self._clean_text(text)
        
        return "No content found"
    
    def _extract_product_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract product information"""
        product_info = {
            "content": self._extract_general_content(soup),
            "price": "",
            "availability": "",
            "rating": ""
        }
        
        # Extract price
        price_selectors = [".price", "[class*='price']", "[data-price]"]
        for selector in price_selectors:
            price_elem = soup.select_one(selector)
            if price_elem:
                product_info["price"] = price_elem.get_text(strip=True)
                break
        
        # Extract availability
        availability_keywords = ["in stock", "out of stock", "available", "unavailable"]
        for keyword in availability_keywords:
            elem = soup.find(text=re.compile(keyword, re.I))
            if elem:
                product_info["availability"] = keyword
                break
        
        return product_info
    
    def _extract_social_content(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract social media content"""
        return {
            "content": self._extract_general_content(soup),
            "author": self._extract_author(soup),
            "timestamp": self._extract_timestamp(soup)
        }
    
    def _extract_author(self, soup: BeautifulSoup) -> str:
        """Extract author information"""
        author_selectors = [
            "[rel='author']",
            ".author",
            "[class*='author']",
            "[data-author]"
        ]
        
        for selector in author_selectors:
            author_elem = soup.select_one(selector)
            if author_elem:
                return author_elem.get_text(strip=True)
        
        return ""
    
    def _extract_timestamp(self, soup: BeautifulSoup) -> str:
        """Extract timestamp information"""
        time_elem = soup.find("time")
        if time_elem:
            return time_elem.get("datetime", time_elem.get_text(strip=True))
        
        return ""
    
    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """Extract all links from the page"""
        links = []
        
        for link in soup.find_all("a", href=True):
            href = link["href"]
            text = link.get_text(strip=True)
            
            # Convert relative URLs to absolute
            full_url = urljoin(base_url, href)
            
            if text and full_url:
                links.append({
                    "url": full_url,
                    "text": text
                })
        
        return links[:20]  # Limit to first 20 links
    
    def _extract_images(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """Extract images from the page"""
        images = []
        
        for img in soup.find_all("img", src=True):
            src = img["src"]
            alt = img.get("alt", "")
            
            # Convert relative URLs to absolute
            full_url = urljoin(base_url, src)
            
            images.append({
                "url": full_url,
                "alt": alt
            })
        
        return images[:10]  # Limit to first 10 images
    
    def _clean_text(self, text: str) -> str:
        """Clean and format extracted text"""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove empty lines
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        text = '\n'.join(lines)
        
        # Limit length
        if len(text) > 3000:
            text = text[:3000] + "..."
        
        return text.strip()
    
    async def close(self):
        """Close the session"""
        if self.session and not self.session.closed:
            await self.session.close()

# Convenience function for quick scraping
async def scrape_url(url: str, content_type: str = "auto") -> Dict[str, Any]:
    """Quick function to scrape a single URL"""
    async with WebScraper() as scraper:
        return await scraper.extract_content(url, content_type)
