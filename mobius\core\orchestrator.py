"""
Agent Orchestrator for Mobius AI Assistant
Main coordinator that routes tasks to specialized agents
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from ..agents.base_agent import BaseAgent, AgentResult, agent_registry
from ..memory.memory_manager import memory_manager
from .llm_engine import llm_engine

logger = logging.getLogger(__name__)

class TaskRequest:
    """Represents a user task request"""

    def __init__(self, user_input: str, context: Optional[Dict] = None):
        self.user_input = user_input
        self.context = context or {}
        self.timestamp = datetime.now()
        self.id = f"task_{self.timestamp.strftime('%Y%m%d_%H%M%S')}_{hash(user_input) % 1000}"

class Orchestrator:
    """Main orchestrator that coordinates between LLM, agents, and memory"""

    def __init__(self):
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.task_history: List[Dict[str, Any]] = []
        self.is_initialized = False

    async def initialize(self):
        """Initialize the orchestrator and load model"""
        if not self.is_initialized:
            logger.info("Initializing Mobius Orchestrator...")

            # Load LLM model
            if not llm_engine.load_model():
                logger.error("Failed to load LLM model")
                return False

            self.is_initialized = True
            logger.info("Orchestrator initialized successfully")
            return True
        return True

    async def process_request(self, user_input: str) -> str:
        """Main entry point for processing user requests"""
        if not self.is_initialized:
            await self.initialize()

        try:
            # Check for special commands
            if user_input.lower().strip() in ["exit", "quit", "bye"]:
                return "Goodbye! Mobius session ended."

            if "remember" in user_input.lower() and ("this" in user_input.lower() or "that" in user_input.lower()):
                # Extract what to remember
                content = user_input.replace("remember", "").replace("this", "").replace("that", "").strip()
                if content:
                    memory_manager.remember_explicitly(content)
                    response = f"I'll remember: {content}"
                else:
                    response = "What would you like me to remember?"
            else:
                # Generate response using LLM
                context = memory_manager.get_context_for_llm()
                prompt = f"""Context: {context}

User: {user_input}