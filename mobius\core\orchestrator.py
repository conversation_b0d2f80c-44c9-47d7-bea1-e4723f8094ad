"""
Agent Orchestrator for Mobius AI Assistant
Main coordinator that routes tasks to specialized agents
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from ..agents.base_agent import BaseAgent, AgentResult, agent_registry
from ..memory.memory_manager import memory_manager
from .llm_engine import llm_engine

logger = logging.getLogger(__name__)

class TaskRequest:
    """Represents a user task request"""

    def __init__(self, user_input: str, context: Optional[Dict] = None):
        self.user_input = user_input
        self.context = context or {}
        self.timestamp = datetime.now()
        self.id = f"task_{self.timestamp.strftime('%Y%m%d_%H%M%S')}_{hash(user_input) % 1000}"

class Orchestrator:
    """Main orchestrator that coordinates between LLM, agents, and memory"""

    def __init__(self):
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.task_history: List[Dict[str, Any]] = []
        self.is_initialized = False

    async def initialize(self):
        """Initialize the orchestrator and load model"""
        if not self.is_initialized:
            logger.info("Initializing Mobius Orchestrator...")

            # Load LLM model
            if not llm_engine.load_model():
                logger.error("Failed to load LLM model")
                return False

            self.is_initialized = True
            logger.info("Orchestrator initialized successfully")
            return True
        return True

    async def process_request(self, user_input: str) -> str:
        """Main entry point for processing user requests"""
        if not self.is_initialized:
            await self.initialize()

        try:
            # Check for special commands
            if user_input.lower().strip() in ["exit", "quit", "bye"]:
                return "Goodbye! Mobius session ended."

            if "remember" in user_input.lower() and ("this" in user_input.lower() or "that" in user_input.lower()):
                # Extract what to remember
                content = user_input.replace("remember", "").replace("this", "").replace("that", "").strip()
                if content:
                    memory_manager.remember_explicitly(content)
                    response = f"I'll remember: {content}"
                else:
                    response = "What would you like me to remember?"
            else:
                # Analyze intent and route to appropriate handler
                task_request = TaskRequest(user_input)
                response = await self._handle_task_request(task_request)

            # Store conversation in memory
            memory_manager.add_conversation(user_input, response)

            return response

        except Exception as e:
            logger.error(f"Error processing request: {str(e)}")
            return f"I encountered an error: {str(e)}"

    async def _handle_task_request(self, task_request: TaskRequest) -> str:
        """Analyze intent and route to appropriate handler"""
        user_input = task_request.user_input.lower()

        # Intent classification
        intent = self._classify_intent(user_input)

        if intent == "web_search":
            return await self._handle_web_search(task_request)
        elif intent == "file_operation":
            return await self._handle_file_operation(task_request)
        elif intent == "system_command":
            return await self._handle_system_command(task_request)
        elif intent == "memory_search":
            return await self._handle_memory_search(task_request)
        else:
            # Default to conversation
            return await self._handle_conversation(task_request)

    def _classify_intent(self, user_input: str) -> str:
        """Classify user intent based on input"""
        user_input = user_input.lower()

        # Web search indicators
        web_keywords = ["search", "lookup", "find", "google", "web", "internet", "browse", "website", "url"]
        if any(keyword in user_input for keyword in web_keywords):
            return "web_search"

        # File operation indicators
        file_keywords = ["file", "folder", "directory", "save", "open", "read", "write", "create", "delete", "copy", "move"]
        if any(keyword in user_input for keyword in file_keywords):
            return "file_operation"

        # System command indicators
        system_keywords = ["run", "execute", "command", "terminal", "cmd", "powershell", "install", "uninstall"]
        if any(keyword in user_input for keyword in system_keywords):
            return "system_command"

        # Memory search indicators
        memory_keywords = ["recall", "what did", "remember when", "previous", "earlier", "before"]
        if any(keyword in user_input for keyword in memory_keywords):
            return "memory_search"

        return "conversation"

    async def _handle_web_search(self, task_request: TaskRequest) -> str:
        """Handle web search requests"""
        # Find web search agent
        capable_agents = agent_registry.find_capable_agents("web_search", {"query": task_request.user_input})

        if capable_agents:
            agent = capable_agents[0]
            task = {
                "type": "web_search",
                "query": task_request.user_input,
                "max_results": 5
            }
            result = await agent.run_task(task)

            if result.success:
                return result.data
            else:
                return f"Web search failed: {result.message}"
        else:
            return "Web search capability not available. Let me try to help with the information I have."

    async def _handle_file_operation(self, task_request: TaskRequest) -> str:
        """Handle file operation requests"""
        capable_agents = agent_registry.find_capable_agents("file_operation", {"request": task_request.user_input})

        if capable_agents:
            agent = capable_agents[0]
            task = {
                "type": "file_operation",
                "request": task_request.user_input,
                "context": task_request.context
            }
            result = await agent.run_task(task)

            if result.success:
                return result.data
            else:
                return f"File operation failed: {result.message}"
        else:
            return "File operation capability not available."

    async def _handle_system_command(self, task_request: TaskRequest) -> str:
        """Handle system command requests"""
        capable_agents = agent_registry.find_capable_agents("system_command", {"command": task_request.user_input})

        if capable_agents:
            agent = capable_agents[0]
            task = {
                "type": "system_command",
                "command": task_request.user_input,
                "context": task_request.context
            }
            result = await agent.run_task(task)

            if result.success:
                return result.data
            else:
                return f"System command failed: {result.message}"
        else:
            return "System command capability not available."

    async def _handle_memory_search(self, task_request: TaskRequest) -> str:
        """Handle memory search requests"""
        query = task_request.user_input
        results = memory_manager.search_memory(query)

        if "No relevant memories found" in results:
            # Try conversation with context
            return await self._handle_conversation(task_request)
        else:
            return results

    async def _handle_conversation(self, task_request: TaskRequest) -> str:
        """Handle general conversation using LLM"""
        try:
            # Get context from memory
            context = memory_manager.get_context_for_llm()

            # Create prompt for LLM
            prompt = self._create_conversation_prompt(context, task_request.user_input)

            # Generate response
            response = llm_engine.generate_response(prompt, max_new_tokens=512)

            return response.strip()

        except Exception as e:
            logger.error(f"Conversation handling failed: {str(e)}")
            return "I'm having trouble processing that request right now."

    def _create_conversation_prompt(self, context: str, user_input: str) -> str:
        """Create a well-formatted prompt for the LLM"""
        system_prompt = """You are Mobius, a highly intelligent personal AI assistant. You are helpful, knowledgeable, and efficient. You have access to various capabilities through specialized agents including web search, file operations, and system commands.

Key traits:
- Be concise but thorough
- Understand user intent and suggest appropriate actions
- If you need to perform actions (search web, access files, run commands), clearly state what you would do
- Be proactive in offering help
- Maintain context from previous conversations"""

        if context:
            prompt = f"""{system_prompt}

Context from previous interactions:
{context}

User: {user_input}
Mobius:"""
        else:
            prompt = f"""{system_prompt}

User: {user_input}
Mobius:"""

        return prompt

    def get_status(self) -> Dict[str, Any]:
        """Get orchestrator status"""
        return {
            "initialized": self.is_initialized,
            "active_tasks": len(self.active_tasks),
            "registered_agents": len(agent_registry.get_all_agents()),
            "model_loaded": llm_engine.is_loaded,
            "model_info": llm_engine.get_model_info()
        }

    async def shutdown(self):
        """Gracefully shutdown the orchestrator"""
        logger.info("Shutting down Mobius Orchestrator...")

        # Cancel active tasks
        for task_id, task in self.active_tasks.items():
            if not task.done():
                task.cancel()
                logger.info(f"Cancelled task: {task_id}")

        # Clear memory session
        memory_manager.clear_session()

        logger.info("Orchestrator shutdown complete")

# Global instance
orchestrator = Orchestrator()