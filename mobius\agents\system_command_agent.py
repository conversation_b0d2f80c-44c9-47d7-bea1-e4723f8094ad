"""
System Command Agent for Mobius AI Assistant
Handles safe system command execution
"""
import asyncio
import logging
import subprocess
import shlex
from typing import Dict, Any, List, Optional
from .base_agent import BaseAgent, AgentResult, AgentCapability
from ..config.settings import SYSTEM_CONFIG

logger = logging.getLogger(__name__)

class SystemCommandAgent(BaseAgent):
    """Agent for safe system command execution"""
    
    def __init__(self):
        super().__init__(
            name="SystemCommandAgent",
            description="Executes safe system commands with security restrictions",
            capabilities=[
                AgentCapability.SYSTEM_COMMANDS
            ]
        )
        self.blocked_commands = SYSTEM_CONFIG["blocked_commands"]
        self.allowed_commands = [
            "dir", "ls", "pwd", "cd", "echo", "cat", "type", "find", "grep",
            "python", "pip", "node", "npm", "git", "curl", "wget", "ping",
            "ipconfig", "ifconfig", "netstat", "ps", "top", "htop", "whoami",
            "date", "time", "uptime", "df", "du", "free", "which", "where"
        ]
    
    async def execute(self, task: Dict[str, Any]) -> AgentResult:
        """Execute system command task"""
        try:
            task_type = task.get("type")
            
            if task_type == "system_command":
                return await self._execute_command(task)
            else:
                return AgentResult(
                    success=False,
                    message=f"Unknown task type: {task_type}"
                )
                
        except Exception as e:
            logger.error(f"SystemCommandAgent error: {str(e)}")
            return AgentResult(
                success=False,
                message=f"Command execution failed: {str(e)}",
                error=e
            )
    
    def can_handle(self, task_type: str, task_data: Dict[str, Any]) -> bool:
        """Check if this agent can handle the task"""
        return task_type == "system_command"
    
    def _is_command_safe(self, command: str) -> tuple[bool, str]:
        """Check if command is safe to execute"""
        command_lower = command.lower().strip()
        
        # Check for blocked commands
        for blocked in self.blocked_commands:
            if blocked.lower() in command_lower:
                return False, f"Blocked command detected: {blocked}"
        
        # Check for dangerous patterns
        dangerous_patterns = [
            "rm -rf", "del /f /s", "format", "fdisk", "mkfs",
            "dd if=", "sudo rm", "sudo dd", "> /dev/", "shutdown",
            "reboot", "halt", "poweroff", "init 0", "init 6",
            "chmod 777", "chown root", "passwd", "su -", "sudo su"
        ]
        
        for pattern in dangerous_patterns:
            if pattern in command_lower:
                return False, f"Dangerous pattern detected: {pattern}"
        
        # Extract base command
        parts = shlex.split(command)
        if not parts:
            return False, "Empty command"
        
        base_command = parts[0].split('\\')[-1].split('/')[-1]  # Get just the command name
        
        # Check if base command is in allowed list
        if base_command not in self.allowed_commands:
            return False, f"Command not in allowed list: {base_command}"
        
        return True, "Command is safe"
    
    async def _execute_command(self, task: Dict[str, Any]) -> AgentResult:
        """Execute system command safely"""
        command = task.get("command", "")
        
        if not command:
            return AgentResult(
                success=False,
                message="No command provided"
            )
        
        # Extract actual command from user request
        actual_command = self._extract_command_from_request(command)
        
        if not actual_command:
            return AgentResult(
                success=False,
                message="Could not extract valid command from request"
            )
        
        # Check if command is safe
        is_safe, safety_message = self._is_command_safe(actual_command)
        
        if not is_safe:
            return AgentResult(
                success=False,
                message=f"Command blocked for security: {safety_message}"
            )
        
        try:
            # Execute command with timeout
            result = await self._run_command_async(actual_command)
            
            return AgentResult(
                success=True,
                data=result,
                message=f"Command executed: {actual_command}"
            )
            
        except asyncio.TimeoutError:
            return AgentResult(
                success=False,
                message="Command timed out after 30 seconds"
            )
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Command execution failed: {str(e)}",
                error=e
            )
    
    async def _run_command_async(self, command: str, timeout: int = 30) -> str:
        """Run command asynchronously with timeout"""
        try:
            # Create subprocess
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                shell=True
            )
            
            # Wait for completion with timeout
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=timeout
            )
            
            # Decode output
            stdout_text = stdout.decode('utf-8', errors='replace')
            stderr_text = stderr.decode('utf-8', errors='replace')
            
            # Format result
            result = f"Command: {command}\n"
            result += f"Exit code: {process.returncode}\n\n"
            
            if stdout_text:
                result += f"Output:\n{stdout_text}\n"
            
            if stderr_text:
                result += f"Errors:\n{stderr_text}\n"
            
            # Limit output size
            if len(result) > 3000:
                result = result[:3000] + "\n... (output truncated)"
            
            return result
            
        except asyncio.TimeoutError:
            # Kill the process if it's still running
            if process.returncode is None:
                process.kill()
                await process.wait()
            raise
    
    def _extract_command_from_request(self, request: str) -> str:
        """Extract actual command from user request"""
        request = request.strip()
        
        # Common command prefixes to remove
        prefixes = [
            "run", "execute", "command", "cmd", "terminal",
            "please run", "can you run", "execute the command",
            "run the command", "please execute"
        ]
        
        request_lower = request.lower()
        
        # Remove common prefixes
        for prefix in prefixes:
            if request_lower.startswith(prefix):
                request = request[len(prefix):].strip()
                break
        
        # Look for quoted commands
        if '"' in request:
            parts = request.split('"')
            if len(parts) >= 3:
                return parts[1].strip()
        
        if "'" in request:
            parts = request.split("'")
            if len(parts) >= 3:
                return parts[1].strip()
        
        # Look for commands that start with known command names
        words = request.split()
        if words and words[0] in self.allowed_commands:
            return request
        
        # Try to find command after common words
        command_indicators = [":", "->", "run", "execute"]
        for indicator in command_indicators:
            if indicator in request:
                parts = request.split(indicator, 1)
                if len(parts) > 1:
                    potential_command = parts[1].strip()
                    if potential_command:
                        return potential_command
        
        return request
    
    def get_allowed_commands(self) -> List[str]:
        """Get list of allowed commands"""
        return self.allowed_commands.copy()
    
    def get_blocked_patterns(self) -> List[str]:
        """Get list of blocked command patterns"""
        return self.blocked_commands.copy()
    
    async def test_command_safety(self, command: str) -> Dict[str, Any]:
        """Test if a command would be safe to execute (without executing it)"""
        is_safe, message = self._is_command_safe(command)
        
        return {
            "command": command,
            "is_safe": is_safe,
            "message": message,
            "extracted_command": self._extract_command_from_request(command)
        }
