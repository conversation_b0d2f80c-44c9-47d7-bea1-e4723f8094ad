#!/usr/bin/env python3
"""
Mobius AI Assistant Optimization Script
Optimizes performance and checks system resources
"""
import os
import sys
import psutil
import torch
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_system_resources():
    """Check system resources and provide recommendations"""
    print("🔍 Checking system resources...")
    
    # Memory check
    memory = psutil.virtual_memory()
    memory_gb = memory.total / (1024**3)
    
    print(f"💾 RAM: {memory_gb:.1f} GB total, {memory.percent}% used")
    
    if memory_gb < 8:
        print("⚠️  Warning: Less than 8GB RAM detected. Consider:")
        print("   - Using CPU instead of GPU")
        print("   - Enabling aggressive quantization")
        print("   - Reducing context length")
    elif memory_gb >= 16:
        print("✅ Sufficient RAM for optimal performance")
    
    # GPU check
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        print(f"🎮 GPU: {gpu_count} CUDA device(s) available")
        
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
            print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
            
            if gpu_memory < 6:
                print(f"   ⚠️  GPU {i} has limited memory. Consider CPU mode.")
            elif gpu_memory >= 8:
                print(f"   ✅ GPU {i} suitable for Llama 3.1 8B")
    else:
        print("🎮 GPU: No CUDA devices available (using CPU)")
        print("   💡 CPU mode will be slower but still functional")
    
    # Disk space check
    disk = psutil.disk_usage(str(project_root))
    disk_free_gb = disk.free / (1024**3)
    
    print(f"💿 Disk: {disk_free_gb:.1f} GB free")
    
    if disk_free_gb < 10:
        print("⚠️  Warning: Low disk space. Model downloads require ~15GB")
    else:
        print("✅ Sufficient disk space")

def optimize_configuration():
    """Generate optimized configuration based on system"""
    print("\n⚙️ Generating optimized configuration...")
    
    # Import after adding to path
    from mobius.config.settings import LLM_CONFIG
    
    memory = psutil.virtual_memory()
    memory_gb = memory.total / (1024**3)
    has_gpu = torch.cuda.is_available()
    
    optimizations = []
    
    # Memory optimizations
    if memory_gb < 12:
        optimizations.append("Enable 4-bit quantization")
        optimizations.append("Reduce max_length to 4096")
        optimizations.append("Limit session memory to 20 exchanges")
    
    # GPU optimizations
    if has_gpu:
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        if gpu_memory < 8:
            optimizations.append("Use 8-bit quantization instead of 4-bit")
            optimizations.append("Enable gradient checkpointing")
    else:
        optimizations.append("Use CPU with float32 precision")
        optimizations.append("Reduce batch size to 1")
    
    # Performance optimizations
    optimizations.extend([
        "Enable model compilation with torch.compile",
        "Use attention optimization",
        "Enable memory-efficient attention"
    ])
    
    print("📋 Recommended optimizations:")
    for i, opt in enumerate(optimizations, 1):
        print(f"   {i}. {opt}")
    
    # Generate optimized config
    optimized_config = generate_optimized_config(memory_gb, has_gpu)
    
    config_file = project_root / "mobius" / "config" / "optimized_settings.py"
    with open(config_file, 'w') as f:
        f.write(optimized_config)
    
    print(f"✅ Optimized configuration saved to {config_file}")

def generate_optimized_config(memory_gb: float, has_gpu: bool) -> str:
    """Generate optimized configuration code"""
    
    # Determine optimal settings
    if memory_gb < 8:
        max_length = 2048
        quantization_bits = 4
        session_limit = 15
    elif memory_gb < 16:
        max_length = 4096
        quantization_bits = 4
        session_limit = 30
    else:
        max_length = 8192
        quantization_bits = 4 if has_gpu else 8
        session_limit = 50
    
    device = "cuda" if has_gpu else "cpu"
    
    config_code = f'''"""
Optimized configuration for Mobius AI Assistant
Auto-generated based on system resources
"""
import os
from pathlib import Path

# Base paths
PROJECT_ROOT = Path(__file__).parent.parent.parent
MOBIUS_ROOT = PROJECT_ROOT / "mobius"
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"

# Create directories
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Optimized LLM Configuration
LLM_CONFIG = {{
    "model_name": "meta-llama/Meta-Llama-3.1-8B-Instruct",
    "max_length": {max_length},
    "temperature": 0.7,
    "top_p": 0.9,
    "do_sample": True,
    "device": "{device}",
    "quantization": {{
        "enabled": True,
        "bits": {quantization_bits},
        "type": "nf4"
    }},
    # Performance optimizations
    "use_cache": True,
    "torch_compile": {str(has_gpu).lower()},
    "attention_optimization": True
}}

# Optimized Memory Configuration
MEMORY_CONFIG = {{
    "session_memory_limit": {session_limit},
    "persistent_memory_file": DATA_DIR / "persistent_memory.json",
    "summary_trigger_length": {max(5, session_limit // 5)},
    "max_context_tokens": {max_length // 2}
}}

# Performance-tuned Agent Configuration
AGENT_CONFIG = {{
    "max_concurrent_agents": {2 if memory_gb < 8 else 3},
    "agent_timeout": 300,
    "retry_attempts": 2
}}

# System Configuration
SYSTEM_CONFIG = {{
    "allow_file_operations": True,
    "allow_system_commands": True,
    "allow_web_access": True,
    "safe_directories": [
        str(PROJECT_ROOT),
        os.path.expanduser("~/Documents"),
        os.path.expanduser("~/Desktop")
    ],
    "blocked_commands": [
        "rm -rf /",
        "del /f /s /q C:\\\\*",
        "format",
        "shutdown",
        "reboot"
    ]
}}

# Web Configuration
WEB_CONFIG = {{
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "timeout": 30,
    "max_retries": 3,
    "rate_limit": 1.0
}}

# Logging Configuration
LOGGING_CONFIG = {{
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": LOGS_DIR / "mobius.log",
    "max_size": 10 * 1024 * 1024,
    "backup_count": 5
}}

# Environment variables
HF_TOKEN = os.getenv("HF_TOKEN")
'''
    
    return config_code

def check_model_availability():
    """Check if the model is available and suggest alternatives"""
    print("\n🤖 Checking model availability...")
    
    try:
        from transformers import AutoTokenizer
        
        model_name = "meta-llama/Meta-Llama-3.1-8B-Instruct"
        
        # Try to load tokenizer (lighter than full model)
        print(f"   Checking access to {model_name}...")
        
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            token=os.getenv("HF_TOKEN")
        )
        
        print("✅ Model access confirmed")
        
    except Exception as e:
        print(f"❌ Model access failed: {e}")
        print("\n💡 Suggestions:")
        print("   1. Set HF_TOKEN environment variable")
        print("   2. Request access to Llama 3.1 on Hugging Face")
        print("   3. Consider alternative models:")
        print("      - microsoft/Phi-3.5-mini-instruct (smaller, no gating)")
        print("      - mistralai/Mistral-7B-Instruct-v0.3")

def run_performance_test():
    """Run a quick performance test"""
    print("\n⚡ Running performance test...")
    
    try:
        import time
        import torch
        
        # Test tensor operations
        start_time = time.time()
        
        if torch.cuda.is_available():
            device = "cuda"
            x = torch.randn(1000, 1000).to(device)
            y = torch.randn(1000, 1000).to(device)
            z = torch.matmul(x, y)
            torch.cuda.synchronize()
        else:
            device = "cpu"
            x = torch.randn(1000, 1000)
            y = torch.randn(1000, 1000)
            z = torch.matmul(x, y)
        
        elapsed = time.time() - start_time
        
        print(f"✅ Matrix multiplication test ({device}): {elapsed:.3f}s")
        
        if elapsed < 0.1:
            print("   🚀 Excellent performance")
        elif elapsed < 0.5:
            print("   ✅ Good performance")
        elif elapsed < 2.0:
            print("   ⚠️  Moderate performance")
        else:
            print("   🐌 Slow performance - consider optimizations")
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")

def main():
    """Main optimization entry point"""
    print("🚀 Mobius AI Assistant Optimization Tool\n")
    
    # Run all checks
    check_system_resources()
    optimize_configuration()
    check_model_availability()
    run_performance_test()
    
    print("\n" + "="*60)
    print("🎯 Optimization complete!")
    print("\nNext steps:")
    print("1. Review the optimized configuration")
    print("2. Set HF_TOKEN environment variable if needed")
    print("3. Run: python test_mobius.py")
    print("4. Run: python run_mobius.py")

if __name__ == "__main__":
    main()
