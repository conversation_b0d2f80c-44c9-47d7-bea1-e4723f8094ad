"""
Main Application Interface for Mobius AI Assistant
Entry point for the Mobius personal AI assistant
"""
import asyncio
import logging
import sys
import signal
from pathlib import Path
from typing import Optional
import colorama
from colorama import Fore, Style

# Setup logging
from config.settings import LOGGING_CONFIG
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG["level"]),
    format=LOGGING_CONFIG["format"],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG["file"]),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Import core components
from core.orchestrator import orchestrator
from agents.agent_loader import initialize_agents
from memory.memory_manager import memory_manager

class MobiusInterface:
    """Main interface for Mobius AI Assistant"""
    
    def __init__(self):
        self.running = False
        self.session_active = False
        
        # Initialize colorama for colored output
        colorama.init()
    
    async def start(self):
        """Start Mobius AI Assistant"""
        try:
            print(f"{Fore.CYAN}🤖 Initializing Mobius AI Assistant...{Style.RESET_ALL}")
            
            # Initialize agents
            if not initialize_agents():
                print(f"{Fore.RED}❌ Failed to initialize agents{Style.RESET_ALL}")
                return False
            
            # Initialize orchestrator
            if not await orchestrator.initialize():
                print(f"{Fore.RED}❌ Failed to initialize orchestrator{Style.RESET_ALL}")
                return False
            
            print(f"{Fore.GREEN}✅ Mobius AI Assistant initialized successfully{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}💡 Type 'help' for commands, 'exit' to quit{Style.RESET_ALL}")
            
            self.running = True
            self.session_active = True
            
            # Setup signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # Start main interaction loop
            await self._interaction_loop()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Mobius: {str(e)}")
            print(f"{Fore.RED}❌ Failed to start Mobius: {str(e)}{Style.RESET_ALL}")
            return False
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n{Fore.YELLOW}🛑 Received shutdown signal...{Style.RESET_ALL}")
        self.running = False
    
    async def _interaction_loop(self):
        """Main interaction loop"""
        print(f"\n{Fore.MAGENTA}🚀 Mobius is ready! How can I help you today?{Style.RESET_ALL}\n")
        
        while self.running:
            try:
                # Get user input
                user_input = await self._get_user_input()
                
                if not user_input or not user_input.strip():
                    continue
                
                # Handle special commands
                if await self._handle_special_commands(user_input):
                    continue
                
                # Process request through orchestrator
                print(f"{Fore.BLUE}🤔 Processing...{Style.RESET_ALL}")
                
                response = await orchestrator.process_request(user_input)
                
                # Display response
                self._display_response(response)
                
                # Auto-summarize if needed
                await memory_manager.auto_summarize_if_needed()
                
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}🛑 Interrupted by user{Style.RESET_ALL}")
                break
            except Exception as e:
                logger.error(f"Error in interaction loop: {str(e)}")
                print(f"{Fore.RED}❌ An error occurred: {str(e)}{Style.RESET_ALL}")
        
        await self._shutdown()
    
    async def _get_user_input(self) -> str:
        """Get user input asynchronously"""
        loop = asyncio.get_event_loop()
        
        # Use thread pool for input to avoid blocking
        prompt = f"{Fore.GREEN}You: {Style.RESET_ALL}"
        
        try:
            user_input = await loop.run_in_executor(None, input, prompt)
            return user_input.strip()
        except EOFError:
            return "exit"
    
    async def _handle_special_commands(self, user_input: str) -> bool:
        """Handle special commands, return True if handled"""
        command = user_input.lower().strip()
        
        if command in ["exit", "quit", "bye", "goodbye"]:
            print(f"{Fore.CYAN}👋 Goodbye! Mobius session ending...{Style.RESET_ALL}")
            self.running = False
            return True
        
        elif command == "help":
            self._show_help()
            return True
        
        elif command == "status":
            await self._show_status()
            return True
        
        elif command == "clear":
            self._clear_screen()
            return True
        
        elif command == "memory":
            self._show_memory_info()
            return True
        
        elif command.startswith("remember "):
            content = user_input[9:].strip()
            if content:
                result = memory_manager.remember_explicitly(content)
                print(f"{Fore.GREEN}✅ {result}{Style.RESET_ALL}")
            else:
                print(f"{Fore.YELLOW}⚠️ Please specify what to remember{Style.RESET_ALL}")
            return True
        
        return False
    
    def _show_help(self):
        """Display help information"""
        help_text = f"""
{Fore.CYAN}🤖 Mobius AI Assistant - Help{Style.RESET_ALL}

{Fore.YELLOW}Special Commands:{Style.RESET_ALL}
  help          - Show this help message
  status        - Show system status
  memory        - Show memory information
  clear         - Clear screen
  remember <X>  - Explicitly remember something
  exit/quit     - Exit Mobius

{Fore.YELLOW}Capabilities:{Style.RESET_ALL}
  🔍 Web Search    - "search for Python tutorials"
  📁 File Ops      - "read file.txt", "list files"
  💻 System Cmds   - "run python --version"
  🧠 Memory        - "what did we discuss about X?"
  💬 Conversation  - General chat and assistance

{Fore.YELLOW}Examples:{Style.RESET_ALL}
  "search for latest AI news"
  "read the contents of config.py"
  "list files in the current directory"
  "run git status"
  "remember that I prefer Python over JavaScript"
"""
        print(help_text)
    
    async def _show_status(self):
        """Show system status"""
        status = orchestrator.get_status()
        
        print(f"\n{Fore.CYAN}📊 Mobius System Status{Style.RESET_ALL}")
        print(f"  Initialized: {Fore.GREEN if status['initialized'] else Fore.RED}{status['initialized']}{Style.RESET_ALL}")
        print(f"  Active Tasks: {status['active_tasks']}")
        print(f"  Registered Agents: {status['registered_agents']}")
        print(f"  Model Loaded: {Fore.GREEN if status['model_loaded'] else Fore.RED}{status['model_loaded']}{Style.RESET_ALL}")
        
        if status['model_info']:
            model_info = status['model_info']
            print(f"  Model: {model_info['model_name']}")
            print(f"  Device: {model_info['device']}")
            print(f"  Quantization: {model_info['quantization_enabled']}")
        
        print()
    
    def _clear_screen(self):
        """Clear the screen"""
        import os
        os.system('cls' if os.name == 'nt' else 'clear')
        print(f"{Fore.MAGENTA}🤖 Mobius AI Assistant{Style.RESET_ALL}\n")
    
    def _show_memory_info(self):
        """Show memory information"""
        session_count = len(memory_manager.session.conversations)
        persistent_count = len(memory_manager.persistent.memories)
        
        print(f"\n{Fore.CYAN}🧠 Memory Information{Style.RESET_ALL}")
        print(f"  Session Conversations: {session_count}")
        print(f"  Persistent Memories: {persistent_count}")
        
        if memory_manager.session.context_summary:
            print(f"  Current Context: {memory_manager.session.context_summary}")
        
        # Show recent memories
        recent = memory_manager.persistent.get_recent_memories(3)
        if recent:
            print(f"\n  Recent Memories:")
            for memory in recent:
                print(f"    - {memory.content[:100]}...")
        
        print()
    
    def _display_response(self, response: str):
        """Display assistant response with formatting"""
        print(f"\n{Fore.MAGENTA}Mobius: {Style.RESET_ALL}{response}\n")
    
    async def _shutdown(self):
        """Graceful shutdown"""
        print(f"{Fore.CYAN}🔄 Shutting down Mobius...{Style.RESET_ALL}")
        
        try:
            # Shutdown orchestrator
            await orchestrator.shutdown()
            
            # Clear session memory
            memory_manager.clear_session()
            
            print(f"{Fore.GREEN}✅ Mobius shutdown complete{Style.RESET_ALL}")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {str(e)}")
            print(f"{Fore.RED}❌ Error during shutdown: {str(e)}{Style.RESET_ALL}")

async def main():
    """Main entry point"""
    interface = MobiusInterface()
    await interface.start()

def run():
    """Synchronous entry point"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}👋 Goodbye!{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}❌ Fatal error: {str(e)}{Style.RESET_ALL}")
        sys.exit(1)

if __name__ == "__main__":
    run()
