import torch
import gc
import os
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
# import gradio as gr  # For web UI; comment out for terminal-only

# Model paths (download from HF and set local paths)
LIGHT_MODEL = "microsoft/Phi-3.5-mini-instruct"  # 3.8B, 128k context
HEAVY_MODEL = "meta-llama/Meta-Llama-3.1-8B-Instruct"  # 8B, 128k context, gated (needs HF token)

# Quantization config (apply on load, GPU-only)
quant_config = BitsAndBytesConfig(load_in_4bit=True, bnb_4bit_quant_type="nf4")  # Or 5bit for better quality

# Shared session history (list of dicts)
history = []  # List of {"user": msg, "assistant": reply}

current_model = None
current_tokenizer = None
device = "cuda" if torch.cuda.is_available() else "cpu"
MAX_HISTORY = 20  # Limit to last N exchanges to avoid context overflow
MAX_TOKENS = 80000  # Rough token limit for prompt building (under 128k)

def load_model(model_name, quant=False):
    global current_model, current_tokenizer
    # Unload previous to free memory
    if current_model is not None:
        del current_model
    if current_tokenizer is not None:
        del current_tokenizer
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()
    
    try:
        # Disable quant if on CPU (BnB requires CUDA)
        if quant and device != "cuda":
            quant = False
            print("Warning: Quantization disabled on CPU (BitsAndBytes requires CUDA). Loading unquantized.")
        
        kwargs = {
            "trust_remote_code": True,  # Required for Phi models
            "token": os.getenv("HF_TOKEN")  # For gated models like Llama; set env var HF_TOKEN=your_token
        }
        if quant:
            kwargs["quantization_config"] = quant_config
        
        current_model = AutoModelForCausalLM.from_pretrained(model_name, **kwargs).to(device)
        current_tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True, token=os.getenv("HF_TOKEN"))
        
        # Set pad_token if not set (avoids warnings)
        if current_tokenizer.pad_token is None:
            current_tokenizer.pad_token = current_tokenizer.eos_token
        
        return f"Loaded: {model_name} (quant={quant}, device={device})"
    except Exception as e:
        return f"Error loading model: {str(e)}"

def truncate_history():
    global history
    # Keep last MAX_HISTORY
    history = history[-MAX_HISTORY:]
    # More precise token check (adds minor overhead)
    temp_prompt = "\n".join([f"User: {h['user']}\nAssistant: {h['assistant']}" for h in history])
    if current_tokenizer and len(current_tokenizer.encode(temp_prompt)) > MAX_TOKENS:
        history = history[-MAX_HISTORY // 2:]  # Halve if still over

def generate_response(user_input):
    global history
    if not current_model or not current_tokenizer:
        msg = load_model(LIGHT_MODEL, quant=True)
        return f"No model loaded. Auto-loading light model: {msg}"
    
    if "activate mobius" in user_input.lower():
        msg = load_model(HEAVY_MODEL, quant=True)  # Activate heavy with quant
        history.append({"user": user_input, "assistant": f"Mobius activated ({msg}). How can I assist with deeper analysis?"})
        return history[-1]["assistant"]
    
    if "end session" in user_input.lower():
        history = []  # Clear memory
        return "Session ended and memory cleared."
    
    truncate_history()  # Manage history length
    
    # Build prompt with history
    prompt = "\n".join([f"User: {h['user']}\nAssistant: {h['assistant']}" for h in history]) + f"\nUser: {user_input}\nAssistant:"
    inputs = current_tokenizer(prompt, return_tensors="pt").to(device)
    
    # Generate
    outputs = current_model.generate(
        **inputs,
        max_new_tokens=200,
        do_sample=True,
        temperature=0.7,
        pad_token_id=current_tokenizer.pad_token_id  # Avoid warnings
    )
    
    # Decode only new tokens
    new_tokens = outputs[0][inputs['input_ids'].shape[1]:]
    reply = current_tokenizer.decode(new_tokens, skip_special_tokens=True).strip()
    
    history.append({"user": user_input, "assistant": reply})
    return reply

# Start with light model
print(load_model(LIGHT_MODEL, quant=True))  # Print load result for visibility

# Terminal loop (or use Gradio for UI)
while True:
    user_input = input("You: ")
    if user_input.lower() == "exit":
        break
    print("Assistant:", generate_response(user_input))

# For Gradio web UI (uncomment):
# gr.Interface(fn=generate_response, inputs="text", outputs="text").launch()