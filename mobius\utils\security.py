"""
Security utilities for Mobius AI Assistant
Provides sandboxing and security measures for system access
"""
import os
import re
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from ..config.settings import SYSTEM_CONFIG

logger = logging.getLogger(__name__)

class SecurityManager:
    """Manages security policies and access controls"""
    
    def __init__(self):
        self.safe_directories = [Path(d).resolve() for d in SYSTEM_CONFIG["safe_directories"]]
        self.blocked_commands = SYSTEM_CONFIG["blocked_commands"]
        self.allowed_file_extensions = {
            'read': ['.txt', '.md', '.py', '.js', '.json', '.yaml', '.yml', '.xml', '.csv', '.log'],
            'write': ['.txt', '.md', '.json', '.yaml', '.yml', '.csv', '.log'],
            'execute': ['.py', '.js', '.sh', '.bat', '.cmd']
        }
        
    def validate_file_access(self, file_path: str, operation: str) -> Tuple[bool, str]:
        """Validate file access request"""
        try:
            path = Path(file_path).resolve()
            
            # Check if path is within safe directories
            if not self._is_path_safe(path):
                return False, f"Access denied: {file_path} is outside safe directories"
            
            # Check file extension for operation
            if not self._is_extension_allowed(path, operation):
                return False, f"File extension not allowed for {operation}: {path.suffix}"
            
            # Check for dangerous file patterns
            if self._is_dangerous_file(path):
                return False, f"Access denied: {file_path} matches dangerous file pattern"
            
            return True, "Access granted"
            
        except Exception as e:
            logger.error(f"File access validation error: {str(e)}")
            return False, f"Validation error: {str(e)}"
    
    def validate_command(self, command: str) -> Tuple[bool, str]:
        """Validate system command for execution"""
        command_lower = command.lower().strip()
        
        # Check for blocked commands
        for blocked in self.blocked_commands:
            if blocked.lower() in command_lower:
                return False, f"Blocked command detected: {blocked}"
        
        # Check for dangerous patterns
        dangerous_patterns = [
            r'rm\s+-rf\s*/',
            r'del\s+/[fs]\s+/[sq]',
            r'format\s+[a-z]:',
            r'fdisk\s+',
            r'mkfs\.',
            r'dd\s+if=',
            r'sudo\s+rm',
            r'sudo\s+dd',
            r'>\s*/dev/',
            r'shutdown',
            r'reboot',
            r'halt',
            r'poweroff',
            r'init\s+[06]',
            r'chmod\s+777',
            r'chown\s+root',
            r'passwd\s+',
            r'su\s+-',
            r'sudo\s+su'
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, command_lower):
                return False, f"Dangerous command pattern detected: {pattern}"
        
        # Check for command injection attempts
        injection_patterns = [
            r'[;&|`$()]',  # Command separators and substitution
            r'<\s*\(',     # Process substitution
            r'>\s*\(',     # Process substitution
        ]
        
        for pattern in injection_patterns:
            if re.search(pattern, command):
                return False, f"Potential command injection detected: {pattern}"
        
        return True, "Command is safe"
    
    def sanitize_command(self, command: str) -> str:
        """Sanitize command by removing dangerous elements"""
        # Remove dangerous characters
        sanitized = re.sub(r'[;&|`$()]', '', command)
        
        # Remove multiple spaces
        sanitized = re.sub(r'\s+', ' ', sanitized)
        
        return sanitized.strip()
    
    def validate_web_url(self, url: str) -> Tuple[bool, str]:
        """Validate web URL for access"""
        # Block local/private network access
        blocked_hosts = [
            'localhost',
            '127.0.0.1',
            '0.0.0.0',
            '::1',
            '10.',
            '172.16.',
            '172.17.',
            '172.18.',
            '172.19.',
            '172.20.',
            '172.21.',
            '172.22.',
            '172.23.',
            '172.24.',
            '172.25.',
            '172.26.',
            '172.27.',
            '172.28.',
            '172.29.',
            '172.30.',
            '172.31.',
            '192.168.'
        ]
        
        url_lower = url.lower()
        
        for blocked_host in blocked_hosts:
            if blocked_host in url_lower:
                return False, f"Access to local/private network blocked: {blocked_host}"
        
        # Check for valid protocols
        if not url_lower.startswith(('http://', 'https://')):
            return False, "Only HTTP and HTTPS protocols are allowed"
        
        return True, "URL is safe"
    
    def _is_path_safe(self, path: Path) -> bool:
        """Check if path is within safe directories"""
        try:
            for safe_dir in self.safe_directories:
                try:
                    path.relative_to(safe_dir)
                    return True
                except ValueError:
                    continue
            return False
        except Exception:
            return False
    
    def _is_extension_allowed(self, path: Path, operation: str) -> bool:
        """Check if file extension is allowed for operation"""
        if operation not in self.allowed_file_extensions:
            return False
        
        allowed_extensions = self.allowed_file_extensions[operation]
        
        # Allow files without extensions for certain operations
        if not path.suffix and operation in ['read', 'execute']:
            return True
        
        return path.suffix.lower() in allowed_extensions
    
    def _is_dangerous_file(self, path: Path) -> bool:
        """Check if file matches dangerous patterns"""
        dangerous_patterns = [
            r'\.exe$',
            r'\.bat$',
            r'\.cmd$',
            r'\.com$',
            r'\.scr$',
            r'\.pif$',
            r'\.vbs$',
            r'\.js$',  # JavaScript files can be dangerous
            r'\.jar$',
            r'\.msi$',
            r'\.dll$',
            r'\.sys$'
        ]
        
        filename = path.name.lower()
        
        for pattern in dangerous_patterns:
            if re.search(pattern, filename):
                return True
        
        # Check for system files
        system_files = [
            'hosts',
            'passwd',
            'shadow',
            'sudoers',
            'boot.ini',
            'ntldr',
            'bootmgr'
        ]
        
        return filename in system_files
    
    def create_sandbox_environment(self) -> Dict[str, Any]:
        """Create a sandboxed environment for command execution"""
        # Limit environment variables
        safe_env = {
            'PATH': os.environ.get('PATH', ''),
            'HOME': os.environ.get('HOME', ''),
            'USER': os.environ.get('USER', ''),
            'LANG': os.environ.get('LANG', 'en_US.UTF-8'),
            'TERM': os.environ.get('TERM', 'xterm')
        }
        
        return safe_env
    
    def log_security_event(self, event_type: str, details: str, severity: str = "INFO"):
        """Log security-related events"""
        log_message = f"SECURITY [{severity}] {event_type}: {details}"
        
        if severity == "CRITICAL":
            logger.critical(log_message)
        elif severity == "ERROR":
            logger.error(log_message)
        elif severity == "WARNING":
            logger.warning(log_message)
        else:
            logger.info(log_message)

class AccessControl:
    """Manages access control policies"""
    
    def __init__(self):
        self.permissions = {
            'file_read': True,
            'file_write': True,
            'file_execute': False,  # Disabled by default
            'system_commands': True,
            'web_access': True,
            'network_access': False  # Disabled by default
        }
        
    def check_permission(self, permission: str) -> bool:
        """Check if a permission is granted"""
        return self.permissions.get(permission, False)
    
    def grant_permission(self, permission: str):
        """Grant a permission"""
        if permission in self.permissions:
            self.permissions[permission] = True
            logger.info(f"Permission granted: {permission}")
    
    def revoke_permission(self, permission: str):
        """Revoke a permission"""
        if permission in self.permissions:
            self.permissions[permission] = False
            logger.info(f"Permission revoked: {permission}")
    
    def get_permissions(self) -> Dict[str, bool]:
        """Get current permissions"""
        return self.permissions.copy()

# Global instances
security_manager = SecurityManager()
access_control = AccessControl()
