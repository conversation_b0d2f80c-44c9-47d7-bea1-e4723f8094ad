"""
File System Agent for Mobius AI Assistant
Handles file and directory operations safely
"""
import os
import shutil
import logging
import aiofiles
from pathlib import Path
from typing import Dict, Any, List, Optional
from .base_agent import BaseAgent, AgentResult, AgentCapability
from ..config.settings import SYSTEM_CONFIG

logger = logging.getLogger(__name__)

class FileSystemAgent(BaseAgent):
    """Agent for safe file system operations"""
    
    def __init__(self):
        super().__init__(
            name="FileSystemAgent",
            description="Performs safe file and directory operations",
            capabilities=[
                AgentCapability.FILE_SYSTEM
            ]
        )
        self.safe_directories = [Path(d) for d in SYSTEM_CONFIG["safe_directories"]]
    
    async def execute(self, task: Dict[str, Any]) -> AgentResult:
        """Execute file system task"""
        try:
            task_type = task.get("type")
            
            if task_type == "file_operation":
                return await self._handle_file_operation(task)
            else:
                return AgentResult(
                    success=False,
                    message=f"Unknown task type: {task_type}"
                )
                
        except Exception as e:
            logger.error(f"FileSystemAgent error: {str(e)}")
            return AgentResult(
                success=False,
                message=f"File operation failed: {str(e)}",
                error=e
            )
    
    def can_handle(self, task_type: str, task_data: Dict[str, Any]) -> bool:
        """Check if this agent can handle the task"""
        return task_type == "file_operation"
    
    def _is_safe_path(self, path: Path) -> bool:
        """Check if path is within safe directories"""
        try:
            path = path.resolve()
            for safe_dir in self.safe_directories:
                try:
                    path.relative_to(safe_dir.resolve())
                    return True
                except ValueError:
                    continue
            return False
        except Exception:
            return False
    
    async def _handle_file_operation(self, task: Dict[str, Any]) -> AgentResult:
        """Handle file operation based on user request"""
        request = task.get("request", "").lower()
        
        # Parse the request to determine operation
        if any(word in request for word in ["read", "open", "show", "display", "cat"]):
            return await self._read_file(task)
        elif any(word in request for word in ["write", "create", "save"]):
            return await self._write_file(task)
        elif any(word in request for word in ["list", "ls", "dir", "show files"]):
            return await self._list_directory(task)
        elif any(word in request for word in ["copy", "cp"]):
            return await self._copy_file(task)
        elif any(word in request for word in ["move", "mv", "rename"]):
            return await self._move_file(task)
        elif any(word in request for word in ["delete", "remove", "rm"]):
            return await self._delete_file(task)
        elif any(word in request for word in ["mkdir", "create directory", "create folder"]):
            return await self._create_directory(task)
        else:
            return AgentResult(
                success=False,
                message="Could not determine file operation. Please specify: read, write, list, copy, move, delete, or create directory."
            )
    
    async def _read_file(self, task: Dict[str, Any]) -> AgentResult:
        """Read file content"""
        file_path = self._extract_path_from_request(task.get("request", ""))
        
        if not file_path:
            return AgentResult(
                success=False,
                message="Please specify a file path to read"
            )
        
        path = Path(file_path)
        
        if not self._is_safe_path(path):
            return AgentResult(
                success=False,
                message=f"Access denied: {file_path} is outside safe directories"
            )
        
        try:
            if not path.exists():
                return AgentResult(
                    success=False,
                    message=f"File not found: {file_path}"
                )
            
            if not path.is_file():
                return AgentResult(
                    success=False,
                    message=f"Path is not a file: {file_path}"
                )
            
            async with aiofiles.open(path, 'r', encoding='utf-8') as f:
                content = await f.read()
            
            # Limit content size for display
            if len(content) > 5000:
                content = content[:5000] + "\n... (content truncated)"
            
            return AgentResult(
                success=True,
                data=f"Content of {file_path}:\n\n{content}",
                message=f"Successfully read {file_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Failed to read file: {str(e)}",
                error=e
            )
    
    async def _write_file(self, task: Dict[str, Any]) -> AgentResult:
        """Write content to file"""
        request = task.get("request", "")
        file_path = self._extract_path_from_request(request)
        content = task.get("content", "")
        
        if not file_path:
            return AgentResult(
                success=False,
                message="Please specify a file path to write to"
            )
        
        if not content:
            return AgentResult(
                success=False,
                message="Please provide content to write"
            )
        
        path = Path(file_path)
        
        if not self._is_safe_path(path):
            return AgentResult(
                success=False,
                message=f"Access denied: {file_path} is outside safe directories"
            )
        
        try:
            # Create parent directories if they don't exist
            path.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiofiles.open(path, 'w', encoding='utf-8') as f:
                await f.write(content)
            
            return AgentResult(
                success=True,
                data=f"Successfully wrote {len(content)} characters to {file_path}",
                message=f"File written: {file_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Failed to write file: {str(e)}",
                error=e
            )
    
    async def _list_directory(self, task: Dict[str, Any]) -> AgentResult:
        """List directory contents"""
        dir_path = self._extract_path_from_request(task.get("request", ""))
        
        if not dir_path:
            dir_path = "."  # Current directory
        
        path = Path(dir_path)
        
        if not self._is_safe_path(path):
            return AgentResult(
                success=False,
                message=f"Access denied: {dir_path} is outside safe directories"
            )
        
        try:
            if not path.exists():
                return AgentResult(
                    success=False,
                    message=f"Directory not found: {dir_path}"
                )
            
            if not path.is_dir():
                return AgentResult(
                    success=False,
                    message=f"Path is not a directory: {dir_path}"
                )
            
            items = []
            for item in path.iterdir():
                if item.is_dir():
                    items.append(f"📁 {item.name}/")
                else:
                    size = item.stat().st_size
                    items.append(f"📄 {item.name} ({self._format_size(size)})")
            
            if not items:
                content = f"Directory {dir_path} is empty"
            else:
                content = f"Contents of {dir_path}:\n\n" + "\n".join(sorted(items))
            
            return AgentResult(
                success=True,
                data=content,
                message=f"Listed {len(items)} items in {dir_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Failed to list directory: {str(e)}",
                error=e
            )
    
    async def _create_directory(self, task: Dict[str, Any]) -> AgentResult:
        """Create directory"""
        dir_path = self._extract_path_from_request(task.get("request", ""))
        
        if not dir_path:
            return AgentResult(
                success=False,
                message="Please specify a directory path to create"
            )
        
        path = Path(dir_path)
        
        if not self._is_safe_path(path):
            return AgentResult(
                success=False,
                message=f"Access denied: {dir_path} is outside safe directories"
            )
        
        try:
            path.mkdir(parents=True, exist_ok=True)
            
            return AgentResult(
                success=True,
                data=f"Directory created: {dir_path}",
                message=f"Successfully created directory: {dir_path}"
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Failed to create directory: {str(e)}",
                error=e
            )
    
    def _extract_path_from_request(self, request: str) -> str:
        """Extract file/directory path from user request"""
        # Simple path extraction - look for quoted paths or paths with extensions
        words = request.split()
        
        for word in words:
            # Remove quotes if present
            word = word.strip('"\'')
            
            # Check if it looks like a path
            if ('/' in word or '\\' in word or 
                '.' in word and not word.startswith('.') or
                word.endswith(('.txt', '.py', '.json', '.md', '.log'))):
                return word
        
        return ""
    
    def _format_size(self, size: int) -> str:
        """Format file size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    # Additional methods for copy, move, delete operations would go here
    # Keeping within 300 line limit for initial implementation
