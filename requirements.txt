# Core ML and AI
torch>=2.0.0
transformers>=4.35.0
accelerate>=0.24.0
bitsandbytes>=0.41.0
sentencepiece>=0.1.99

# Web scraping and requests
requests>=2.31.0
beautifulsoup4>=4.12.0
selenium>=4.15.0
aiohttp>=3.9.0

# System interaction
psutil>=5.9.0
pyautogui>=0.9.54
keyboard>=0.13.5
mouse>=0.7.1

# File and data handling
python-dotenv>=1.0.0
pyyaml>=6.0.1
jsonschema>=4.19.0
pandas>=2.1.0

# Async and concurrency
asyncio-mqtt>=0.16.0
aiofiles>=23.2.1

# Development and testing
pytest>=7.4.0
black>=23.9.0
flake8>=6.1.0

# UI and display
colorama>=0.4.6

# Optional: Voice capabilities (uncomment if needed)
# speech-recognition>=3.10.0
# pyttsx3>=2.90

# Optional: GUI (uncomment if needed)
# tkinter (built-in)
# PyQt6>=6.6.0
