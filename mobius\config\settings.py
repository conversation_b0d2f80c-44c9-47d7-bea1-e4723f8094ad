"""
Configuration settings for Mobius AI Assistant
"""
import os
from pathlib import Path

# Base paths
PROJECT_ROOT = Path(__file__).parent.parent.parent
MOBIUS_ROOT = PROJECT_ROOT / "mobius"
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"

# Create directories if they don't exist
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# LLM Configuration
LLM_CONFIG = {
    "model_name": "meta-llama/Meta-Llama-3.1-8B-Instruct",
    "max_length": 8192,
    "temperature": 0.7,
    "top_p": 0.9,
    "do_sample": True,
    "device": "cuda" if os.environ.get("CUDA_AVAILABLE", "true").lower() == "true" else "cpu",
    "quantization": {
        "enabled": True,
        "bits": 4,
        "type": "nf4"
    }
}

# Memory Configuration
MEMORY_CONFIG = {
    "session_memory_limit": 50,  # Number of exchanges to keep in session
    "persistent_memory_file": DATA_DIR / "persistent_memory.json",
    "summary_trigger_length": 10,  # Summarize after N important exchanges
    "max_context_tokens": 6000  # Reserve tokens for context
}

# Agent Configuration
AGENT_CONFIG = {
    "max_concurrent_agents": 3,
    "agent_timeout": 300,  # 5 minutes
    "retry_attempts": 2
}

# System Access Configuration
SYSTEM_CONFIG = {
    "allow_file_operations": True,
    "allow_system_commands": True,
    "allow_web_access": True,
    "safe_directories": [
        str(PROJECT_ROOT),
        os.path.expanduser("~/Documents"),
        os.path.expanduser("~/Desktop")
    ],
    "blocked_commands": [
        "rm -rf /",
        "del /f /s /q C:\\*",
        "format",
        "shutdown",
        "reboot"
    ]
}

# Web Scraping Configuration
WEB_CONFIG = {
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "timeout": 30,
    "max_retries": 3,
    "rate_limit": 1.0  # Seconds between requests
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": LOGS_DIR / "mobius.log",
    "max_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5
}

# Environment variables
HF_TOKEN = os.getenv("HF_TOKEN")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")  # For fallback if needed
