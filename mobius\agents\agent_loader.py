"""
Agent Loader for Mobius AI Assistant
Initializes and registers all available agents
"""
import logging
from .base_agent import agent_registry
from .web_search_agent import WebSearchAgent
from .filesystem_agent import FileSystemAgent
from .system_command_agent import SystemCommandAgent

logger = logging.getLogger(__name__)

def initialize_agents():
    """Initialize and register all agents"""
    try:
        logger.info("Initializing Mobius agents...")
        
        # Create and register agents
        agents = [
            WebSearchAgent(),
            FileSystemAgent(),
            SystemCommandAgent()
        ]
        
        for agent in agents:
            agent_registry.register_agent(agent)
            logger.info(f"Registered agent: {agent.name}")
        
        logger.info(f"Successfully initialized {len(agents)} agents")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize agents: {str(e)}")
        return False

def get_agent_status():
    """Get status of all registered agents"""
    agents = agent_registry.get_all_agents()
    
    status = {
        "total_agents": len(agents),
        "agents": []
    }
    
    for agent in agents:
        agent_info = agent.get_info()
        status["agents"].append(agent_info)
    
    return status

def find_agents_for_task(task_type: str, task_data: dict):
    """Find agents capable of handling a specific task"""
    return agent_registry.find_capable_agents(task_type, task_data)
