#!/usr/bin/env python3
"""
Mobius AI Assistant Test Script
Tests core functionality and components
"""
import asyncio
import sys
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_imports():
    """Test that all modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        # Core imports
        from mobius.core.llm_engine import llm_engine
        from mobius.core.orchestrator import orchestrator
        from mobius.memory.memory_manager import memory_manager
        
        # Agent imports
        from mobius.agents.base_agent import agent_registry
        from mobius.agents.web_search_agent import WebSearchAgent
        from mobius.agents.filesystem_agent import FileSystemAgent
        from mobius.agents.system_command_agent import SystemCommandAgent
        from mobius.agents.agent_loader import initialize_agents
        
        # Utility imports
        from mobius.utils.security import security_manager
        from mobius.utils.web_scraper import WebScraper
        
        print("✅ All imports successful")
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

async def test_agent_initialization():
    """Test agent initialization"""
    print("🤖 Testing agent initialization...")
    
    try:
        from mobius.agents.agent_loader import initialize_agents
        from mobius.agents.base_agent import agent_registry
        
        # Initialize agents
        success = initialize_agents()
        if not success:
            print("❌ Agent initialization failed")
            return False
        
        # Check registered agents
        agents = agent_registry.get_all_agents()
        print(f"✅ Initialized {len(agents)} agents:")
        for agent in agents:
            print(f"   - {agent.name}: {agent.description}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent initialization error: {e}")
        return False

async def test_memory_system():
    """Test memory system"""
    print("🧠 Testing memory system...")
    
    try:
        from mobius.memory.memory_manager import memory_manager
        
        # Test session memory
        memory_manager.add_conversation("Hello", "Hi there!")
        memory_manager.add_conversation("How are you?", "I'm doing well!")
        
        # Test context retrieval
        context = memory_manager.get_context_for_llm()
        if "Hello" in context and "Hi there" in context:
            print("✅ Session memory working")
        else:
            print("❌ Session memory failed")
            return False
        
        # Test explicit memory
        result = memory_manager.remember_explicitly("Test memory item")
        if "Remembered" in result:
            print("✅ Persistent memory working")
        else:
            print("❌ Persistent memory failed")
            return False
        
        # Test memory search
        search_result = memory_manager.search_memory("test")
        print(f"✅ Memory search working: {len(search_result)} chars returned")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory system error: {e}")
        return False

async def test_security_system():
    """Test security system"""
    print("🔒 Testing security system...")
    
    try:
        from mobius.utils.security import security_manager
        
        # Test file access validation
        safe_file = str(project_root / "README.md")
        is_safe, message = security_manager.validate_file_access(safe_file, "read")
        if is_safe:
            print("✅ File access validation working")
        else:
            print(f"❌ File access validation failed: {message}")
            return False
        
        # Test command validation
        safe_command = "python --version"
        is_safe, message = security_manager.validate_command(safe_command)
        if is_safe:
            print("✅ Command validation working")
        else:
            print(f"❌ Command validation failed: {message}")
            return False
        
        # Test dangerous command blocking
        dangerous_command = "rm -rf /"
        is_safe, message = security_manager.validate_command(dangerous_command)
        if not is_safe:
            print("✅ Dangerous command blocking working")
        else:
            print("❌ Dangerous command blocking failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Security system error: {e}")
        return False

async def test_web_scraper():
    """Test web scraping capabilities"""
    print("🌐 Testing web scraper...")
    
    try:
        from mobius.utils.web_scraper import WebScraper
        
        # Test with a simple, reliable URL
        test_url = "https://httpbin.org/html"
        
        async with WebScraper() as scraper:
            result = await scraper.extract_content(test_url)
            
            if result and "title" in result:
                print("✅ Web scraper working")
                print(f"   Title: {result['title']}")
                return True
            else:
                print("❌ Web scraper failed to extract content")
                return False
        
    except Exception as e:
        print(f"❌ Web scraper error: {e}")
        print("⚠️  This might be due to network issues - not necessarily a code problem")
        return True  # Don't fail the test for network issues

async def test_orchestrator():
    """Test orchestrator initialization"""
    print("🎭 Testing orchestrator...")
    
    try:
        from mobius.core.orchestrator import orchestrator
        
        # Test initialization (without loading the heavy model)
        # We'll just test the structure
        status = orchestrator.get_status()
        
        if isinstance(status, dict) and "initialized" in status:
            print("✅ Orchestrator structure working")
            print(f"   Status keys: {list(status.keys())}")
            return True
        else:
            print("❌ Orchestrator structure failed")
            return False
        
    except Exception as e:
        print(f"❌ Orchestrator error: {e}")
        return False

async def test_configuration():
    """Test configuration loading"""
    print("⚙️ Testing configuration...")
    
    try:
        from mobius.config.settings import (
            LLM_CONFIG, MEMORY_CONFIG, AGENT_CONFIG, 
            SYSTEM_CONFIG, WEB_CONFIG, LOGGING_CONFIG
        )
        
        # Check that all configs are loaded
        configs = {
            "LLM_CONFIG": LLM_CONFIG,
            "MEMORY_CONFIG": MEMORY_CONFIG,
            "AGENT_CONFIG": AGENT_CONFIG,
            "SYSTEM_CONFIG": SYSTEM_CONFIG,
            "WEB_CONFIG": WEB_CONFIG,
            "LOGGING_CONFIG": LOGGING_CONFIG
        }
        
        for name, config in configs.items():
            if not isinstance(config, dict) or not config:
                print(f"❌ {name} not loaded properly")
                return False
        
        print("✅ All configurations loaded")
        print(f"   Model: {LLM_CONFIG['model_name']}")
        print(f"   Device: {LLM_CONFIG['device']}")
        print(f"   Safe directories: {len(SYSTEM_CONFIG['safe_directories'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

async def run_all_tests():
    """Run all tests"""
    print("🚀 Starting Mobius component tests...\n")
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("Agent Initialization", test_agent_initialization),
        ("Memory System", test_memory_system),
        ("Security System", test_security_system),
        ("Web Scraper", test_web_scraper),
        ("Orchestrator", test_orchestrator)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if await test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
    
    print(f"\n{'='*50}")
    print(f"🧪 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Mobius is ready to run.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

def main():
    """Main test entry point"""
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test runner crashed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
