# 🤖 Mobius AI Assistant

Your personal AI assistant powered by Llama 3.1 8B with total system access, web scraping capabilities, and privacy-first design.

## ✨ Features

- **🧠 Llama 3.1 8B Intelligence**: Powered by Meta's advanced language model
- **🔍 Web Search & Scraping**: Intelligent web search and content extraction
- **📁 File System Access**: Safe file operations with security sandboxing
- **💻 System Commands**: Execute system commands with safety restrictions
- **🧠 Advanced Memory**: Session and persistent memory with LLM-powered summarization
- **🔒 Privacy-First**: No external APIs, all processing happens locally
- **🎯 Agent-Based Architecture**: Extensible system with specialized agents
- **⚡ Resource Efficient**: Optimized for performance with quantization support

## 🚀 Quick Start

### Prerequisites

- Python 3.9+
- CUDA-compatible GPU (recommended) or CPU
- 8GB+ RAM (16GB+ recommended for GPU)
- 10GB+ free disk space

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mobi
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up Hugging Face token (for Llama 3.1)**
   ```bash
   export HF_TOKEN=your_huggingface_token
   ```

4. **Run Mobius**
   ```bash
   python run_mobius.py
   ```

## 🎮 Usage

### Basic Commands

- **General Chat**: Just type naturally
- **Web Search**: "search for Python tutorials"
- **File Operations**: "read config.py", "list files in Documents"
- **System Commands**: "run python --version", "git status"
- **Memory**: "remember that I prefer dark mode"

### Special Commands

- `help` - Show help information
- `status` - Display system status
- `memory` - Show memory information
- `clear` - Clear screen
- `remember <text>` - Explicitly remember something
- `exit` - Exit Mobius

### Examples

```
You: search for latest AI developments
Mobius: [Performs web search and returns results]

You: read the file README.md
Mobius: [Displays file contents]

You: run git log --oneline -5
Mobius: [Executes command and shows output]

You: remember that I'm working on a Python project
Mobius: ✅ Remembered: that I'm working on a Python project
```

## 🏗️ Architecture

### Core Components

- **Orchestrator**: Main coordinator routing tasks to agents
- **LLM Engine**: Manages Llama 3.1 8B model and inference
- **Memory Manager**: Handles session and persistent memory
- **Agent System**: Specialized agents for different capabilities
- **Security Manager**: Ensures safe system access

### Agents

1. **WebSearchAgent**: Web searching and content extraction
2. **FileSystemAgent**: Safe file and directory operations
3. **SystemCommandAgent**: Secure system command execution

## 🔧 Configuration

Edit `mobius/config/settings.py` to customize:

- **Model Settings**: Model path, quantization, generation parameters
- **Memory Settings**: Memory limits, summarization triggers
- **Security Settings**: Safe directories, blocked commands
- **Web Settings**: User agent, timeouts, rate limits

## 🛡️ Security

Mobius implements multiple security layers:

- **File Access Control**: Restricted to safe directories
- **Command Filtering**: Blocks dangerous system commands
- **Input Sanitization**: Prevents command injection
- **Network Restrictions**: Blocks access to local networks
- **Permission System**: Granular access controls

## 📚 Adding New Agents

### 1. Create Agent Class

```python
from mobius.agents.base_agent import BaseAgent, AgentResult, AgentCapability

class MyAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="MyAgent",
            description="Description of what this agent does",
            capabilities=[AgentCapability.WEB_ACCESS]
        )
    
    async def execute(self, task: Dict[str, Any]) -> AgentResult:
        # Implement your agent logic here
        return AgentResult(success=True, data="Result")
    
    def can_handle(self, task_type: str, task_data: Dict[str, Any]) -> bool:
        return task_type == "my_task_type"
```

### 2. Register Agent

Add to `mobius/agents/agent_loader.py`:

```python
from .my_agent import MyAgent

def initialize_agents():
    agents = [
        # ... existing agents
        MyAgent()
    ]
```

### 3. Update Orchestrator

Add intent classification in `mobius/core/orchestrator.py`:

```python
def _classify_intent(self, user_input: str) -> str:
    # Add your intent detection logic
    if "my_keyword" in user_input:
        return "my_task_type"
```

## 🧠 Memory System

### Session Memory
- Temporary conversation history
- Automatically cleared on exit
- Limited by token count and exchange count

### Persistent Memory
- Long-term storage for important information
- User-requested memories ("remember that...")
- Automatic summarization of important conversations

### Memory Commands
```python
# Explicit memory
memory_manager.remember_explicitly("Important fact")

# Search memory
results = memory_manager.search_memory("query")

# Get context for LLM
context = memory_manager.get_context_for_llm()
```

## 🔍 Web Scraping

### Basic Usage
```python
from mobius.utils.web_scraper import scrape_url

result = await scrape_url("https://example.com")
print(result["content"])
```

### Advanced Features
- Intelligent content extraction
- Article detection
- Product information extraction
- Rate limiting and retries
- Link and image extraction

## 🐛 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce model size or use CPU
   - Enable quantization in settings

2. **Model Loading Fails**
   - Check Hugging Face token
   - Verify internet connection
   - Ensure sufficient disk space

3. **Permission Denied**
   - Check safe directories in settings
   - Verify file permissions

### Logs

Check logs in `logs/mobius.log` for detailed error information.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Meta AI for Llama 3.1
- Hugging Face for transformers
- The open-source AI community

---

**Note**: Mobius is designed for personal use and educational purposes. Always review and understand the code before running it with system access privileges.
