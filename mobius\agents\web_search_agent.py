"""
Web Search Agent for Mobius AI Assistant
Handles web searching and content extraction
"""
import asyncio
import logging
import aiohttp
import time
from typing import Dict, Any, List, Optional
from bs4 import BeautifulSoup
from urllib.parse import quote_plus, urljoin
from .base_agent import BaseAgent, AgentResult, AgentCapability
from ..config.settings import WEB_CONFIG

logger = logging.getLogger(__name__)

class WebSearchAgent(BaseAgent):
    """Agent for web searching and content extraction"""
    
    def __init__(self):
        super().__init__(
            name="WebSearchAgent",
            description="Performs web searches and extracts content from web pages",
            capabilities=[
                AgentCapability.WEB_ACCESS,
                AgentCapability.API_CALLS
            ]
        )
        self.session: Optional[aiohttp.ClientSession] = None
        self.last_request_time = 0
        
    async def execute(self, task: Dict[str, Any]) -> AgentResult:
        """Execute web search task"""
        try:
            task_type = task.get("type")
            
            if task_type == "web_search":
                return await self._perform_search(task)
            elif task_type == "extract_content":
                return await self._extract_content(task)
            else:
                return AgentResult(
                    success=False,
                    message=f"Unknown task type: {task_type}"
                )
                
        except Exception as e:
            logger.error(f"WebSearchAgent error: {str(e)}")
            return AgentResult(
                success=False,
                message=f"Web search failed: {str(e)}",
                error=e
            )
    
    def can_handle(self, task_type: str, task_data: Dict[str, Any]) -> bool:
        """Check if this agent can handle the task"""
        return task_type in ["web_search", "extract_content"]
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if self.session is None or self.session.closed:
            headers = {
                "User-Agent": WEB_CONFIG["user_agent"]
            }
            timeout = aiohttp.ClientTimeout(total=WEB_CONFIG["timeout"])
            self.session = aiohttp.ClientSession(
                headers=headers,
                timeout=timeout
            )
        return self.session
    
    async def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < WEB_CONFIG["rate_limit"]:
            await asyncio.sleep(WEB_CONFIG["rate_limit"] - time_since_last)
        
        self.last_request_time = time.time()
    
    async def _perform_search(self, task: Dict[str, Any]) -> AgentResult:
        """Perform web search using DuckDuckGo"""
        query = task.get("query", "")
        max_results = task.get("max_results", 5)
        
        if not query:
            return AgentResult(
                success=False,
                message="No search query provided"
            )
        
        try:
            await self._rate_limit()
            
            # Use DuckDuckGo for privacy-focused search
            search_url = f"https://duckduckgo.com/html/?q={quote_plus(query)}"
            
            session = await self._get_session()
            
            async with session.get(search_url) as response:
                if response.status != 200:
                    return AgentResult(
                        success=False,
                        message=f"Search request failed with status {response.status}"
                    )
                
                html = await response.text()
                results = self._parse_search_results(html, max_results)
                
                if not results:
                    return AgentResult(
                        success=False,
                        message="No search results found"
                    )
                
                # Format results for user
                formatted_results = self._format_search_results(results, query)
                
                return AgentResult(
                    success=True,
                    data=formatted_results,
                    message=f"Found {len(results)} search results"
                )
                
        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            return AgentResult(
                success=False,
                message=f"Search failed: {str(e)}",
                error=e
            )
    
    def _parse_search_results(self, html: str, max_results: int) -> List[Dict[str, str]]:
        """Parse search results from HTML"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            results = []
            
            # Find result containers (DuckDuckGo specific)
            result_containers = soup.find_all('div', class_='result')
            
            for container in result_containers[:max_results]:
                try:
                    # Extract title
                    title_elem = container.find('a', class_='result__a')
                    title = title_elem.get_text(strip=True) if title_elem else "No title"
                    
                    # Extract URL
                    url = title_elem.get('href') if title_elem else ""
                    
                    # Extract snippet
                    snippet_elem = container.find('a', class_='result__snippet')
                    snippet = snippet_elem.get_text(strip=True) if snippet_elem else "No description"
                    
                    if title and url:
                        results.append({
                            "title": title,
                            "url": url,
                            "snippet": snippet
                        })
                        
                except Exception as e:
                    logger.warning(f"Error parsing result: {str(e)}")
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"Error parsing search results: {str(e)}")
            return []
    
    def _format_search_results(self, results: List[Dict[str, str]], query: str) -> str:
        """Format search results for display"""
        if not results:
            return f"No results found for '{query}'"
        
        formatted = f"Search results for '{query}':\n\n"
        
        for i, result in enumerate(results, 1):
            formatted += f"{i}. **{result['title']}**\n"
            formatted += f"   {result['snippet']}\n"
            formatted += f"   URL: {result['url']}\n\n"
        
        return formatted.strip()
    
    async def _extract_content(self, task: Dict[str, Any]) -> AgentResult:
        """Extract content from a specific URL"""
        url = task.get("url", "")
        
        if not url:
            return AgentResult(
                success=False,
                message="No URL provided for content extraction"
            )
        
        try:
            await self._rate_limit()
            
            session = await self._get_session()
            
            async with session.get(url) as response:
                if response.status != 200:
                    return AgentResult(
                        success=False,
                        message=f"Failed to fetch URL: {response.status}"
                    )
                
                html = await response.text()
                content = self._extract_text_content(html)
                
                return AgentResult(
                    success=True,
                    data=content,
                    message=f"Extracted content from {url}"
                )
                
        except Exception as e:
            logger.error(f"Content extraction failed: {str(e)}")
            return AgentResult(
                success=False,
                message=f"Content extraction failed: {str(e)}",
                error=e
            )
    
    def _extract_text_content(self, html: str) -> str:
        """Extract clean text content from HTML"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text content
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            # Limit length
            if len(text) > 2000:
                text = text[:2000] + "..."
            
            return text
            
        except Exception as e:
            logger.error(f"Text extraction failed: {str(e)}")
            return "Failed to extract text content"
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.session and not self.session.closed:
            await self.session.close()
