"""
Base Agent Framework for Mobius AI Assistant
Provides the foundation for all specialized agents
"""
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
from enum import Enum
from datetime import datetime

logger = logging.getLogger(__name__)

class AgentStatus(Enum):
    IDLE = "idle"
    WORKING = "working"
    ERROR = "error"
    COMPLETED = "completed"

class AgentCapability(Enum):
    """Defines what capabilities an agent has"""
    WEB_ACCESS = "web_access"
    FILE_SYSTEM = "file_system"
    SYSTEM_COMMANDS = "system_commands"
    API_CALLS = "api_calls"
    USER_INTERACTION = "user_interaction"

class AgentResult:
    """Represents the result of an agent's work"""
    
    def __init__(self, success: bool, data: Any = None, message: str = "", 
                 error: Optional[Exception] = None):
        self.success = success
        self.data = data
        self.message = message
        self.error = error
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "success": self.success,
            "data": self.data,
            "message": self.message,
            "error": str(self.error) if self.error else None,
            "timestamp": self.timestamp.isoformat()
        }

class BaseAgent(ABC):
    """
    Base class for all Mobius agents
    """
    
    def __init__(self, name: str, description: str, capabilities: List[AgentCapability]):
        self.name = name
        self.description = description
        self.capabilities = capabilities
        self.status = AgentStatus.IDLE
        self.last_result: Optional[AgentResult] = None
        self.created_at = datetime.now()
        self.task_count = 0
        
        # Event callbacks
        self.on_status_change: Optional[Callable] = None
        self.on_result: Optional[Callable] = None
    
    @abstractmethod
    async def execute(self, task: Dict[str, Any]) -> AgentResult:
        """
        Execute a task. Must be implemented by subclasses.
        
        Args:
            task: Dictionary containing task parameters
            
        Returns:
            AgentResult with success status and data
        """
        pass
    
    @abstractmethod
    def can_handle(self, task_type: str, task_data: Dict[str, Any]) -> bool:
        """
        Check if this agent can handle a specific task type
        
        Args:
            task_type: Type of task (e.g., "web_search", "file_operation")
            task_data: Task parameters
            
        Returns:
            True if agent can handle the task
        """
        pass
    
    def get_capabilities(self) -> List[str]:
        """Get list of agent capabilities"""
        return [cap.value for cap in self.capabilities]
    
    def has_capability(self, capability: AgentCapability) -> bool:
        """Check if agent has a specific capability"""
        return capability in self.capabilities
    
    async def run_task(self, task: Dict[str, Any]) -> AgentResult:
        """
        Run a task with status management and error handling
        """
        try:
            self._set_status(AgentStatus.WORKING)
            logger.info(f"Agent {self.name} starting task: {task.get('type', 'unknown')}")
            
            result = await self.execute(task)
            
            self.last_result = result
            self.task_count += 1
            
            if result.success:
                self._set_status(AgentStatus.COMPLETED)
                logger.info(f"Agent {self.name} completed task successfully")
            else:
                self._set_status(AgentStatus.ERROR)
                logger.error(f"Agent {self.name} task failed: {result.message}")
            
            if self.on_result:
                self.on_result(self, result)
            
            return result
            
        except Exception as e:
            error_result = AgentResult(
                success=False,
                message=f"Agent {self.name} encountered an error",
                error=e
            )
            self.last_result = error_result
            self._set_status(AgentStatus.ERROR)
            logger.error(f"Agent {self.name} error: {str(e)}")
            
            if self.on_result:
                self.on_result(self, error_result)
            
            return error_result
        finally:
            # Reset to idle after a delay
            await asyncio.sleep(1)
            self._set_status(AgentStatus.IDLE)
    
    def _set_status(self, status: AgentStatus):
        """Set agent status and trigger callback"""
        old_status = self.status
        self.status = status
        
        if self.on_status_change and old_status != status:
            self.on_status_change(self, old_status, status)
    
    def get_info(self) -> Dict[str, Any]:
        """Get agent information"""
        return {
            "name": self.name,
            "description": self.description,
            "capabilities": self.get_capabilities(),
            "status": self.status.value,
            "task_count": self.task_count,
            "created_at": self.created_at.isoformat(),
            "last_result": self.last_result.to_dict() if self.last_result else None
        }

class AgentPlugin:
    """
    Plugin system for extending agent capabilities
    """
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.enabled = True
    
    async def initialize(self, agent: BaseAgent) -> bool:
        """Initialize plugin for an agent"""
        return True
    
    async def cleanup(self, agent: BaseAgent):
        """Cleanup plugin resources"""
        pass
    
    def get_commands(self) -> Dict[str, Callable]:
        """Get commands provided by this plugin"""
        return {}

class AgentRegistry:
    """
    Registry for managing available agents
    """
    
    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.plugins: Dict[str, AgentPlugin] = {}
    
    def register_agent(self, agent: BaseAgent):
        """Register an agent"""
        self.agents[agent.name] = agent
        logger.info(f"Registered agent: {agent.name}")
    
    def unregister_agent(self, name: str):
        """Unregister an agent"""
        if name in self.agents:
            del self.agents[name]
            logger.info(f"Unregistered agent: {name}")
    
    def get_agent(self, name: str) -> Optional[BaseAgent]:
        """Get agent by name"""
        return self.agents.get(name)
    
    def find_capable_agents(self, task_type: str, task_data: Dict[str, Any]) -> List[BaseAgent]:
        """Find agents capable of handling a task"""
        capable_agents = []
        for agent in self.agents.values():
            if agent.can_handle(task_type, task_data):
                capable_agents.append(agent)
        return capable_agents
    
    def get_all_agents(self) -> List[BaseAgent]:
        """Get all registered agents"""
        return list(self.agents.values())
    
    def register_plugin(self, plugin: AgentPlugin):
        """Register a plugin"""
        self.plugins[plugin.name] = plugin
        logger.info(f"Registered plugin: {plugin.name}")
    
    def get_plugin(self, name: str) -> Optional[AgentPlugin]:
        """Get plugin by name"""
        return self.plugins.get(name)

# Global registry instance
agent_registry = AgentRegistry()
