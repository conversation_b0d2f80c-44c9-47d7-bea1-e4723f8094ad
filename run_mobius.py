#!/usr/bin/env python3
"""
Mobius AI Assistant Startup Script
Entry point for running the Mobius personal AI assistant
"""
import sys
import os
import asyncio
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_requirements():
    """Check if all required packages are installed"""
    required_packages = [
        'torch',
        'transformers',
        'accelerate',
        'bitsandbytes',
        'aiohttp',
        'beautifulsoup4',
        'colorama'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nPlease install missing packages with:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_environment():
    """Check environment setup"""
    # Check if CUDA is available
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        if cuda_available:
            print(f"✅ CUDA available: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  CUDA not available, using CPU (slower)")
    except ImportError:
        print("❌ PyTorch not installed")
        return False
    
    # Check data directories
    data_dir = project_root / "data"
    logs_dir = project_root / "logs"
    
    data_dir.mkdir(exist_ok=True)
    logs_dir.mkdir(exist_ok=True)
    
    print(f"✅ Data directory: {data_dir}")
    print(f"✅ Logs directory: {logs_dir}")
    
    return True

def show_banner():
    """Show Mobius banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🤖 MOBIUS AI ASSISTANT                                    ║
    ║                                                              ║
    ║    Your Personal AI Assistant with:                         ║
    ║    • Llama 3.1 8B Intelligence                              ║
    ║    • Web Search & Scraping                                  ║
    ║    • File System Access                                     ║
    ║    • System Command Execution                               ║
    ║    • Advanced Memory Management                             ║
    ║    • Privacy-First Design                                   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """Main entry point"""
    show_banner()
    
    print("🔍 Checking system requirements...")
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    print("✅ All checks passed!")
    print("\n🚀 Starting Mobius AI Assistant...\n")
    
    try:
        # Import and run Mobius
        from mobius.main import run
        run()
        
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
