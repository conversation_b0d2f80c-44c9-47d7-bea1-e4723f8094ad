# 🛠️ Mobius Developer Guide

This guide explains how to extend Mobius with new agents and capabilities.

## 🏗️ Architecture Overview

Mobius follows an agent-based architecture where specialized agents handle different types of tasks:

```
User Input → Orchestrator → Intent Classification → Agent Selection → Task Execution → Response
```

### Core Components

1. **Orchestrator** (`mobius/core/orchestrator.py`)
   - Routes tasks to appropriate agents
   - Manages conversation flow
   - Handles intent classification

2. **Base Agent** (`mobius/agents/base_agent.py`)
   - Abstract base class for all agents
   - Defines agent interface and capabilities
   - Provides result handling and status management

3. **Memory Manager** (`mobius/memory/memory_manager.py`)
   - Manages session and persistent memory
   - Provides context for LLM
   - Handles automatic summarization

4. **LLM Engine** (`mobius/core/llm_engine.py`)
   - Manages Llama 3.1 8B model
   - Handles inference and generation
   - Provides token counting utilities

## 🤖 Creating a New Agent

### Step 1: Define Agent Class

Create a new file in `mobius/agents/` (e.g., `my_agent.py`):

```python
"""
My Custom Agent for Mobius AI Assistant
Description of what this agent does
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
from .base_agent import BaseAgent, AgentResult, AgentCapability

logger = logging.getLogger(__name__)

class MyCustomAgent(BaseAgent):
    """Agent for handling custom tasks"""
    
    def __init__(self):
        super().__init__(
            name="MyCustomAgent",
            description="Handles custom task processing",
            capabilities=[
                AgentCapability.WEB_ACCESS,  # If needs web access
                AgentCapability.FILE_SYSTEM,  # If needs file access
                AgentCapability.SYSTEM_COMMANDS,  # If needs system commands
                AgentCapability.API_CALLS,  # If makes API calls
                AgentCapability.USER_INTERACTION  # If needs user interaction
            ]
        )
        # Initialize any agent-specific resources
        self.custom_resource = None
    
    async def execute(self, task: Dict[str, Any]) -> AgentResult:
        """Execute the agent's main task"""
        try:
            task_type = task.get("type")
            
            if task_type == "my_task_type":
                return await self._handle_my_task(task)
            else:
                return AgentResult(
                    success=False,
                    message=f"Unknown task type: {task_type}"
                )
                
        except Exception as e:
            logger.error(f"MyCustomAgent error: {str(e)}")
            return AgentResult(
                success=False,
                message=f"Task failed: {str(e)}",
                error=e
            )
    
    def can_handle(self, task_type: str, task_data: Dict[str, Any]) -> bool:
        """Check if this agent can handle the task"""
        return task_type in ["my_task_type", "another_task_type"]
    
    async def _handle_my_task(self, task: Dict[str, Any]) -> AgentResult:
        """Handle specific task implementation"""
        # Extract parameters from task
        param1 = task.get("param1", "")
        param2 = task.get("param2", 0)
        
        # Validate parameters
        if not param1:
            return AgentResult(
                success=False,
                message="param1 is required"
            )
        
        # Perform the actual work
        result_data = await self._do_work(param1, param2)
        
        return AgentResult(
            success=True,
            data=result_data,
            message=f"Successfully processed {param1}"
        )
    
    async def _do_work(self, param1: str, param2: int) -> str:
        """Implement the actual work logic"""
        # Your implementation here
        await asyncio.sleep(0.1)  # Simulate async work
        return f"Processed {param1} with {param2}"
    
    async def cleanup(self):
        """Cleanup resources when agent is destroyed"""
        if self.custom_resource:
            # Clean up any resources
            pass
```

### Step 2: Register the Agent

Add your agent to `mobius/agents/agent_loader.py`:

```python
from .my_agent import MyCustomAgent

def initialize_agents():
    """Initialize and register all agents"""
    try:
        logger.info("Initializing Mobius agents...")
        
        agents = [
            WebSearchAgent(),
            FileSystemAgent(),
            SystemCommandAgent(),
            MyCustomAgent()  # Add your agent here
        ]
        
        for agent in agents:
            agent_registry.register_agent(agent)
            logger.info(f"Registered agent: {agent.name}")
        
        return True
    except Exception as e:
        logger.error(f"Failed to initialize agents: {str(e)}")
        return False
```

### Step 3: Add Intent Classification

Update the orchestrator to recognize when to use your agent in `mobius/core/orchestrator.py`:

```python
def _classify_intent(self, user_input: str) -> str:
    """Classify user intent based on input"""
    user_input = user_input.lower()
    
    # Add your intent detection
    my_keywords = ["my_keyword", "custom_task", "special_action"]
    if any(keyword in user_input for keyword in my_keywords):
        return "my_task_type"
    
    # ... existing intent classification
    
    return "conversation"
```

### Step 4: Add Handler Method

Add a handler method in the orchestrator:

```python
async def _handle_my_task(self, task_request: TaskRequest) -> str:
    """Handle my custom task requests"""
    capable_agents = agent_registry.find_capable_agents("my_task_type", {"request": task_request.user_input})
    
    if capable_agents:
        agent = capable_agents[0]
        task = {
            "type": "my_task_type",
            "param1": self._extract_param1(task_request.user_input),
            "param2": self._extract_param2(task_request.user_input),
            "context": task_request.context
        }
        result = await agent.run_task(task)
        
        if result.success:
            return result.data
        else:
            return f"Task failed: {result.message}"
    else:
        return "Custom task capability not available."

def _extract_param1(self, user_input: str) -> str:
    """Extract parameter from user input"""
    # Implement parameter extraction logic
    return "extracted_value"
```

## 🔧 Agent Capabilities

### Available Capabilities

```python
class AgentCapability(Enum):
    WEB_ACCESS = "web_access"           # Can access web resources
    FILE_SYSTEM = "file_system"         # Can access file system
    SYSTEM_COMMANDS = "system_commands" # Can execute system commands
    API_CALLS = "api_calls"            # Can make API calls
    USER_INTERACTION = "user_interaction" # Can interact with user
```

### Using Capabilities

```python
# Check if agent has capability
if self.has_capability(AgentCapability.WEB_ACCESS):
    # Perform web-related operations
    pass

# Get all capabilities
capabilities = self.get_capabilities()
```

## 🧠 Memory Integration

### Accessing Memory

```python
from mobius.memory.memory_manager import memory_manager

# Search memory
results = memory_manager.search_memory("query")

# Add explicit memory
memory_manager.remember_explicitly("Important information")

# Get context for LLM
context = memory_manager.get_context_for_llm()
```

### Memory in Agents

```python
async def execute(self, task: Dict[str, Any]) -> AgentResult:
    # Check if we have relevant memories
    query = task.get("query", "")
    memories = memory_manager.search_memory(query)
    
    if memories and "Found in memory:" in memories:
        # Use memory information
        return AgentResult(
            success=True,
            data=f"From memory: {memories}"
        )
    
    # Continue with normal processing
    return await self._process_task(task)
```

## 🔒 Security Considerations

### File Access

```python
from mobius.utils.security import security_manager

# Validate file access
is_safe, message = security_manager.validate_file_access(file_path, "read")
if not is_safe:
    return AgentResult(success=False, message=message)
```

### Command Execution

```python
# Validate command
is_safe, message = security_manager.validate_command(command)
if not is_safe:
    return AgentResult(success=False, message=message)

# Sanitize command
safe_command = security_manager.sanitize_command(command)
```

### Web Access

```python
# Validate URL
is_safe, message = security_manager.validate_web_url(url)
if not is_safe:
    return AgentResult(success=False, message=message)
```

## 🌐 Web Integration

### Using Web Scraper

```python
from mobius.utils.web_scraper import WebScraper

async def _scrape_content(self, url: str) -> str:
    async with WebScraper() as scraper:
        result = await scraper.extract_content(url)
        return result.get("content", "No content found")
```

### HTTP Requests

```python
import aiohttp

async def _make_request(self, url: str) -> Dict[str, Any]:
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            if response.status == 200:
                return await response.json()
            else:
                raise Exception(f"HTTP {response.status}")
```

## 🧪 Testing Agents

### Unit Tests

Create tests in `tests/test_agents/`:

```python
import pytest
from mobius.agents.my_agent import MyCustomAgent

@pytest.mark.asyncio
async def test_my_agent():
    agent = MyCustomAgent()
    
    task = {
        "type": "my_task_type",
        "param1": "test_value",
        "param2": 42
    }
    
    result = await agent.execute(task)
    
    assert result.success
    assert "test_value" in result.data
```

### Integration Tests

```python
@pytest.mark.asyncio
async def test_agent_integration():
    from mobius.agents.agent_loader import initialize_agents
    from mobius.agents.base_agent import agent_registry
    
    # Initialize agents
    assert initialize_agents()
    
    # Find agent
    agents = agent_registry.find_capable_agents("my_task_type", {})
    assert len(agents) > 0
    
    # Test execution
    agent = agents[0]
    result = await agent.run_task({"type": "my_task_type"})
    assert result.success
```

## 📊 Monitoring and Logging

### Logging

```python
import logging

logger = logging.getLogger(__name__)

async def execute(self, task: Dict[str, Any]) -> AgentResult:
    logger.info(f"Agent {self.name} executing task: {task.get('type')}")
    
    try:
        result = await self._process_task(task)
        logger.info(f"Agent {self.name} completed successfully")
        return result
    except Exception as e:
        logger.error(f"Agent {self.name} failed: {str(e)}")
        raise
```

### Performance Monitoring

```python
import time

async def execute(self, task: Dict[str, Any]) -> AgentResult:
    start_time = time.time()
    
    try:
        result = await self._process_task(task)
        
        execution_time = time.time() - start_time
        logger.info(f"Agent {self.name} executed in {execution_time:.2f}s")
        
        return result
    except Exception as e:
        execution_time = time.time() - start_time
        logger.error(f"Agent {self.name} failed after {execution_time:.2f}s: {str(e)}")
        raise
```

## 🚀 Best Practices

1. **Error Handling**: Always wrap agent logic in try-catch blocks
2. **Resource Cleanup**: Implement cleanup methods for resources
3. **Security**: Validate all inputs and use security manager
4. **Logging**: Log important events and errors
5. **Testing**: Write comprehensive tests for your agents
6. **Documentation**: Document your agent's capabilities and usage
7. **Performance**: Consider async operations and resource usage
8. **Memory**: Be mindful of memory usage and cleanup

## 📝 Example: Weather Agent

Here's a complete example of a weather agent:

```python
"""
Weather Agent for Mobius AI Assistant
Provides weather information using a weather API
"""
import aiohttp
import logging
from typing import Dict, Any
from .base_agent import BaseAgent, AgentResult, AgentCapability

logger = logging.getLogger(__name__)

class WeatherAgent(BaseAgent):
    """Agent for weather information"""
    
    def __init__(self):
        super().__init__(
            name="WeatherAgent",
            description="Provides weather information for locations",
            capabilities=[AgentCapability.API_CALLS, AgentCapability.WEB_ACCESS]
        )
        self.api_key = "your_api_key"  # In practice, use environment variable
    
    async def execute(self, task: Dict[str, Any]) -> AgentResult:
        try:
            if task.get("type") == "weather_query":
                return await self._get_weather(task)
            else:
                return AgentResult(success=False, message="Unknown task type")
        except Exception as e:
            logger.error(f"WeatherAgent error: {str(e)}")
            return AgentResult(success=False, message=str(e), error=e)
    
    def can_handle(self, task_type: str, task_data: Dict[str, Any]) -> bool:
        return task_type == "weather_query"
    
    async def _get_weather(self, task: Dict[str, Any]) -> AgentResult:
        location = task.get("location", "")
        
        if not location:
            return AgentResult(success=False, message="Location required")
        
        try:
            url = f"http://api.openweathermap.org/data/2.5/weather"
            params = {
                "q": location,
                "appid": self.api_key,
                "units": "metric"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        weather_info = self._format_weather(data)
                        return AgentResult(success=True, data=weather_info)
                    else:
                        return AgentResult(
                            success=False,
                            message=f"Weather API error: {response.status}"
                        )
        except Exception as e:
            return AgentResult(success=False, message=str(e), error=e)
    
    def _format_weather(self, data: Dict[str, Any]) -> str:
        """Format weather data for display"""
        location = data["name"]
        temp = data["main"]["temp"]
        description = data["weather"][0]["description"]
        humidity = data["main"]["humidity"]
        
        return f"Weather in {location}: {temp}°C, {description}, {humidity}% humidity"
```

This guide should help you create powerful new agents for Mobius. Remember to follow the established patterns and security practices!
