"""
Core LLM Engine for Mobius AI Assistant
Handles Llama 3.1 8B model loading, inference, and memory management
"""
import torch
import gc
import logging
from typing import Optional, Dict, Any, List
from transformers import (
    AutoModelForCausalLM, 
    AutoTokenizer, 
    BitsAndBytesConfig,
    GenerationConfig
)
from ..config.settings import LLM_CONFIG, MEMORY_CONFIG

logger = logging.getLogger(__name__)

class LLMEngine:
    """
    Core LLM Engine managing Llama 3.1 8B model
    """
    
    def __init__(self):
        self.model: Optional[AutoModelForCausalLM] = None
        self.tokenizer: Optional[AutoTokenizer] = None
        self.device = LLM_CONFIG["device"]
        self.is_loaded = False
        self.generation_config = None
        
    def load_model(self) -> bool:
        """Load the Llama 3.1 8B model with quantization"""
        try:
            logger.info(f"Loading model: {LLM_CONFIG['model_name']}")
            
            # Clear any existing model
            self._clear_model()
            
            # Setup quantization if enabled and on CUDA
            quant_config = None
            if (LLM_CONFIG["quantization"]["enabled"] and 
                self.device == "cuda" and torch.cuda.is_available()):
                quant_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_quant_type=LLM_CONFIG["quantization"]["type"],
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_use_double_quant=True
                )
                logger.info("Quantization enabled (4-bit)")
            
            # Model loading arguments
            model_kwargs = {
                "trust_remote_code": True,
                "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32,
                "device_map": "auto" if self.device == "cuda" else None,
            }
            
            if quant_config:
                model_kwargs["quantization_config"] = quant_config
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                LLM_CONFIG["model_name"],
                trust_remote_code=True,
                use_fast=True
            )
            
            # Set pad token if not set
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                LLM_CONFIG["model_name"],
                **model_kwargs
            )
            
            # Move to device if not using device_map
            if model_kwargs.get("device_map") is None:
                self.model = self.model.to(self.device)
            
            # Setup generation config
            self.generation_config = GenerationConfig(
                max_length=LLM_CONFIG["max_length"],
                temperature=LLM_CONFIG["temperature"],
                top_p=LLM_CONFIG["top_p"],
                do_sample=LLM_CONFIG["do_sample"],
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                repetition_penalty=1.1,
                length_penalty=1.0
            )
            
            self.is_loaded = True
            logger.info("Model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load model: {str(e)}")
            self.is_loaded = False
            return False
    
    def generate_response(self, prompt: str, max_new_tokens: int = 512) -> str:
        """Generate response from the model"""
        if not self.is_loaded:
            if not self.load_model():
                return "Error: Failed to load model"
        
        try:
            # Tokenize input
            inputs = self.tokenizer(
                prompt, 
                return_tensors="pt", 
                truncation=True,
                max_length=LLM_CONFIG["max_length"] - max_new_tokens
            ).to(self.device)
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_new_tokens,
                    generation_config=self.generation_config,
                    use_cache=True
                )
            
            # Decode only new tokens
            new_tokens = outputs[0][inputs['input_ids'].shape[1]:]
            response = self.tokenizer.decode(new_tokens, skip_special_tokens=True)
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"Generation failed: {str(e)}")
            return f"Error generating response: {str(e)}"
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text"""
        if not self.tokenizer:
            return len(text.split())  # Rough estimate
        return len(self.tokenizer.encode(text))
    
    def _clear_model(self):
        """Clear model from memory"""
        if self.model is not None:
            del self.model
            self.model = None
        if self.tokenizer is not None:
            del self.tokenizer
            self.tokenizer = None
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        self.is_loaded = False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        return {
            "model_name": LLM_CONFIG["model_name"],
            "device": self.device,
            "is_loaded": self.is_loaded,
            "quantization_enabled": LLM_CONFIG["quantization"]["enabled"],
            "max_length": LLM_CONFIG["max_length"]
        }
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self._clear_model()

# Global instance
llm_engine = LLMEngine()
