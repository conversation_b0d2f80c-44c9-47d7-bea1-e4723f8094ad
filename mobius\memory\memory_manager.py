"""
Memory Management System for Mobius AI Assistant
Handles session memory (temporary) and persistent memory (user-requested)
"""
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from ..config.settings import MEMORY_CONFIG

logger = logging.getLogger(__name__)

class MemoryEntry:
    """Represents a single memory entry"""
    
    def __init__(self, content: str, entry_type: str = "conversation", 
                 importance: int = 1, timestamp: Optional[datetime] = None):
        self.content = content
        self.entry_type = entry_type  # conversation, task, reminder, fact
        self.importance = importance  # 1-5 scale
        self.timestamp = timestamp or datetime.now()
        self.id = f"{self.timestamp.isoformat()}_{hash(content) % 10000}"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "content": self.content,
            "type": self.entry_type,
            "importance": self.importance,
            "timestamp": self.timestamp.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryEntry':
        entry = cls(
            content=data["content"],
            entry_type=data["type"],
            importance=data["importance"],
            timestamp=datetime.fromisoformat(data["timestamp"])
        )
        entry.id = data["id"]
        return entry

class SessionMemory:
    """Manages temporary session memory"""
    
    def __init__(self):
        self.conversations: List[Dict[str, str]] = []
        self.context_summary = ""
        self.current_topic = ""
    
    def add_exchange(self, user_input: str, assistant_response: str):
        """Add a conversation exchange"""
        self.conversations.append({
            "user": user_input,
            "assistant": assistant_response,
            "timestamp": datetime.now().isoformat()
        })
        
        # Trim if too long
        if len(self.conversations) > MEMORY_CONFIG["session_memory_limit"]:
            self.conversations = self.conversations[-MEMORY_CONFIG["session_memory_limit"]:]
    
    def get_context(self, max_tokens: int = None) -> str:
        """Get conversation context as string"""
        if max_tokens is None:
            max_tokens = MEMORY_CONFIG["max_context_tokens"]
        
        context_parts = []
        if self.context_summary:
            context_parts.append(f"Previous context: {self.context_summary}")
        
        # Add recent conversations
        for conv in self.conversations[-10:]:  # Last 10 exchanges
            context_parts.append(f"User: {conv['user']}")
            context_parts.append(f"Assistant: {conv['assistant']}")
        
        context = "\n".join(context_parts)
        
        # Rough token estimation and truncation
        if len(context.split()) > max_tokens:
            words = context.split()
            context = " ".join(words[-max_tokens:])
        
        return context
    
    def clear(self):
        """Clear session memory"""
        self.conversations.clear()
        self.context_summary = ""
        self.current_topic = ""

class PersistentMemory:
    """Manages long-term persistent memory"""
    
    def __init__(self):
        self.memory_file = MEMORY_CONFIG["persistent_memory_file"]
        self.memories: List[MemoryEntry] = []
        self.load_memories()
    
    def load_memories(self):
        """Load memories from file"""
        try:
            if self.memory_file.exists():
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.memories = [MemoryEntry.from_dict(entry) for entry in data]
                logger.info(f"Loaded {len(self.memories)} memories")
        except Exception as e:
            logger.error(f"Failed to load memories: {e}")
            self.memories = []
    
    def save_memories(self):
        """Save memories to file"""
        try:
            self.memory_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                data = [memory.to_dict() for memory in self.memories]
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved {len(self.memories)} memories")
        except Exception as e:
            logger.error(f"Failed to save memories: {e}")
    
    def add_memory(self, content: str, entry_type: str = "fact", importance: int = 3):
        """Add a new memory"""
        memory = MemoryEntry(content, entry_type, importance)
        self.memories.append(memory)
        self.save_memories()
        logger.info(f"Added memory: {content[:50]}...")
    
    def search_memories(self, query: str, limit: int = 5) -> List[MemoryEntry]:
        """Search memories by content"""
        query_lower = query.lower()
        matches = []
        
        for memory in self.memories:
            if query_lower in memory.content.lower():
                matches.append(memory)
        
        # Sort by importance and recency
        matches.sort(key=lambda m: (m.importance, m.timestamp), reverse=True)
        return matches[:limit]
    
    def get_recent_memories(self, limit: int = 10) -> List[MemoryEntry]:
        """Get most recent memories"""
        return sorted(self.memories, key=lambda m: m.timestamp, reverse=True)[:limit]
    
    def get_important_memories(self, min_importance: int = 3, limit: int = 10) -> List[MemoryEntry]:
        """Get important memories"""
        important = [m for m in self.memories if m.importance >= min_importance]
        return sorted(important, key=lambda m: (m.importance, m.timestamp), reverse=True)[:limit]

class MemoryManager:
    """Main memory manager coordinating session and persistent memory"""
    
    def __init__(self):
        self.session = SessionMemory()
        self.persistent = PersistentMemory()
        self.pending_summary = []
    
    def add_conversation(self, user_input: str, assistant_response: str):
        """Add conversation to session memory"""
        self.session.add_exchange(user_input, assistant_response)
        
        # Check if we should trigger summarization
        if len(self.session.conversations) % MEMORY_CONFIG["summary_trigger_length"] == 0:
            self.pending_summary.append({
                "user": user_input,
                "assistant": assistant_response
            })
    
    def remember_explicitly(self, content: str, importance: int = 4):
        """Explicitly remember something (user requested)"""
        self.persistent.add_memory(content, "explicit", importance)
        return f"Remembered: {content}"
    
    def search_memory(self, query: str) -> str:
        """Search both session and persistent memory"""
        results = []
        
        # Search persistent memory
        persistent_matches = self.persistent.search_memories(query)
        for match in persistent_matches:
            results.append(f"[{match.entry_type}] {match.content}")
        
        # Search session memory
        query_lower = query.lower()
        for conv in self.session.conversations[-20:]:  # Last 20 exchanges
            if (query_lower in conv["user"].lower() or 
                query_lower in conv["assistant"].lower()):
                results.append(f"[recent] User: {conv['user'][:100]}...")
        
        if results:
            return "Found in memory:\n" + "\n".join(results[:10])
        else:
            return "No relevant memories found."
    
    def get_context_for_llm(self) -> str:
        """Get context string for LLM"""
        context_parts = []
        
        # Add important persistent memories
        important_memories = self.persistent.get_important_memories(limit=5)
        if important_memories:
            context_parts.append("Important memories:")
            for memory in important_memories:
                context_parts.append(f"- {memory.content}")
        
        # Add session context
        session_context = self.session.get_context()
        if session_context:
            context_parts.append("\nRecent conversation:")
            context_parts.append(session_context)
        
        return "\n".join(context_parts)
    
    def clear_session(self):
        """Clear session memory"""
        self.session.clear()
        logger.info("Session memory cleared")
    
    def create_summary(self, conversations: List[Dict]) -> str:
        """Create summary of conversations (to be enhanced with LLM)"""
        # Simple summary for now - can be enhanced with LLM later
        topics = []
        for conv in conversations:
            # Extract key topics (simple keyword extraction)
            words = conv["user"].lower().split()
            important_words = [w for w in words if len(w) > 4 and w.isalpha()]
            topics.extend(important_words[:3])
        
        if topics:
            return f"Discussed: {', '.join(set(topics[:10]))}"
        return "General conversation"

# Global instance
memory_manager = MemoryManager()
